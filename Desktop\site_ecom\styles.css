/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fefefe;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header et Navigation */
.header {
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8f5 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: #d63384;
    font-size: 1.8rem;
    font-weight: 700;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #d63384;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #d63384;
}

.nav-icons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-icon {
    position: relative;
    color: #333;
    font-size: 1.2rem;
    text-decoration: none;
    transition: color 0.3s ease;
}

.cart-icon:hover {
    color: #d63384;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #d63384;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Boutons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.btn-primary {
    background: linear-gradient(135deg, #d63384 0%, #e91e63 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(214, 51, 132, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-add-cart {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c2c7 100%);
    color: #721c24;
    border: 1px solid #f5c2c7;
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-add-cart:hover {
    background: linear-gradient(135deg, #f5c2c7 0%, #f8d7da 100%);
    transform: translateY(-1px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8f5 100%);
    padding: 4rem 0;
    display: flex;
    align-items: center;
    min-height: 70vh;
}

.hero-content {
    flex: 1;
    padding: 2rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.promo-banner {
    background: linear-gradient(135deg, #d63384 0%, #e91e63 100%);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    text-align: center;
    animation: pulse 2s infinite;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Sections */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(135deg, #d63384 0%, #e91e63 100%);
    border-radius: 2px;
}

/* Produits */
.featured-products,
.products-section {
    padding: 4rem 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 300px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(214, 51, 132, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.btn-quick-view {
    background: white;
    color: #d63384;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-quick-view:hover {
    background: #f8f9fa;
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
    text-align: center;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #d63384;
    margin-bottom: 1rem;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8f5 100%);
    padding: 3rem 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.page-header p {
    font-size: 1.1rem;
    color: #666;
}

/* Filtres */
.filters {
    background: white;
    padding: 2rem 0;
    border-bottom: 1px solid #eee;
}

.filter-bar {
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #333;
}

.filter-group select {
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 0.9rem;
}

/* About Section */
.about {
    padding: 4rem 0;
    background: #f8f9fa;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.about-text p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature i {
    color: #d63384;
    font-size: 1.2rem;
    width: 20px;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #d63384;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #d63384;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #d63384;
    color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #e91e63;
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-card {
    animation: fadeInUp 0.6s ease forwards;
}

/* Panier */
.cart-section {
    padding: 2rem 0;
}

.cart-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.cart-item-image {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    overflow: hidden;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.cart-item-price {
    color: #d63384;
    font-weight: 700;
    font-size: 1.1rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: #f8f9fa;
    border-color: #d63384;
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 5px;
}

.remove-btn {
    color: #dc3545;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.remove-btn:hover {
    color: #c82333;
}

.summary-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    height: fit-content;
}

.summary-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
}

.summary-line.total {
    border-top: 2px solid #dee2e6;
    padding-top: 1rem;
    font-weight: 700;
    font-size: 1.2rem;
    color: #d63384;
}

.promo-code {
    display: flex;
    gap: 0.5rem;
    margin: 1.5rem 0;
}

.promo-code input {
    flex: 1;
    padding: 10px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
}

.btn-checkout {
    width: 100%;
    margin: 1rem 0;
    padding: 15px;
    font-size: 1.1rem;
}

.continue-shopping {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    text-decoration: none;
    justify-content: center;
    margin-top: 1rem;
    transition: color 0.3s ease;
}

.continue-shopping:hover {
    color: #d63384;
}

.empty-cart {
    text-align: center;
    padding: 4rem 0;
}

.empty-cart-content i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-cart-content h2 {
    color: #666;
    margin-bottom: 1rem;
}

/* Contact */
.contact-section {
    padding: 3rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info h2 {
    margin-bottom: 2rem;
    color: #333;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    color: #d63384;
    font-size: 1.2rem;
    margin-top: 0.2rem;
}

.contact-item h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.contact-item p {
    color: #666;
    line-height: 1.6;
}

.social-contact {
    margin-top: 2rem;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.contact-form h2 {
    margin-bottom: 2rem;
    color: #333;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #d63384;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input {
    width: auto;
}

.faq-section {
    padding: 3rem 0;
    background: #f8f9fa;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.faq-item h4 {
    color: #333;
    margin-bottom: 1rem;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

/* Détail produit */
.breadcrumb {
    background: #f8f9fa;
    padding: 1rem 0;
}

.breadcrumb nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #d63384;
}

.product-detail {
    padding: 3rem 0;
}

.product-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.product-images {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.main-image {
    width: 100%;
    height: 500px;
    border-radius: 15px;
    overflow: hidden;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details h1 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 2rem;
    color: #d63384;
    font-weight: 700;
    margin-bottom: 1rem;
}

.product-description {
    color: #666;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.product-options {
    margin-bottom: 2rem;
}

.option-group {
    margin-bottom: 1rem;
}

.option-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.size-options,
.color-options {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.size-option,
.color-option {
    padding: 8px 16px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.size-option:hover,
.color-option:hover,
.size-option.selected,
.color-option.selected {
    border-color: #d63384;
    background: #ffeef8;
}

.add-to-cart-section {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.related-products {
    padding: 3rem 0;
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .about-content,
    .contact-content,
    .product-detail-grid {
        grid-template-columns: 1fr;
    }

    .cart-content {
        grid-template-columns: 1fr;
    }

    .filter-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .container {
        padding: 0 15px;
    }

    .cart-item {
        flex-direction: column;
        text-align: center;
    }

    .add-to-cart-section {
        flex-direction: column;
    }
}

/* Styles pour les promotions sur le site public */
.promotions-section {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.promotions-banner {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.promotions-banner::-webkit-scrollbar {
    display: none;
}

.promo-card {
    background: white;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    min-width: 300px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.promo-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.promo-card.flash {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    animation: pulse-promo 2s infinite;
}

@keyframes pulse-promo {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.promo-icon {
    font-size: 2rem;
    color: #d63384;
}

.promo-content h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.promo-code {
    background: #d63384;
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-weight: 700;
    font-size: 0.9rem;
    margin: 0.5rem 0;
    display: inline-block;
}

.promo-description {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.promo-discount {
    font-size: 1.5rem;
    font-weight: 700;
    color: #d63384;
    margin-left: auto;
}

/* Bannière hero dynamique */
.hero .promo-banner {
    margin-top: 1rem;
}

.hero-promo {
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.hero-promo-icon {
    font-size: 1.5rem;
    color: #d63384;
}

.hero-promo-text {
    color: #333;
    font-weight: 600;
}

.hero-promo-code {
    background: #d63384;
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-weight: 700;
    margin-left: auto;
}

/* Responsive pour les promotions */
@media (max-width: 768px) {
    .promotions-banner {
        flex-direction: column;
        align-items: center;
    }

    .promo-card {
        min-width: 100%;
        max-width: 400px;
    }

    .hero-promo {
        flex-direction: column;
        text-align: center;
    }
}
