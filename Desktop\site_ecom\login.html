<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion Admin - ARWA SHOP</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #ffeef8 0%, #f8e8f5 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Poppins', sans-serif;
        }
        
        .login-container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .login-header {
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #d63384;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
            font-size: 1rem;
        }
        
        .login-form {
            text-align: left;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #d63384;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #d63384 0%, #e91e63 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(214, 51, 132, 0.4);
        }
        
        .back-to-site {
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .back-to-site:hover {
            color: #d63384;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .admin-info {
            background: #e7f3ff;
            color: #0c5460;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            border-left: 4px solid #0dcaf0;
        }
        
        .admin-info strong {
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .security-features {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #dee2e6;
        }
        
        .security-features h3 {
            color: #333;
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        
        .security-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .security-item i {
            color: #28a745;
            width: 16px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-shield-alt"></i> ARWA SHOP</h1>
            <p>Administration sécurisée</p>
        </div>
        
        <div class="admin-info">
            <strong>Informations de connexion :</strong>
            Utilisateur : <code>admin</code><br>
            Mot de passe : <code>arwa2024</code>
        </div>
        
        <div class="error-message" id="errorMessage">
            Nom d'utilisateur ou mot de passe incorrect.
        </div>
        
        <form class="login-form" id="loginForm">
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i> Nom d'utilisateur
                </label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> Mot de passe
                </label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i> Se connecter
            </button>
        </form>
        
        <a href="index.html" class="back-to-site">
            <i class="fas fa-arrow-left"></i> Retour au site
        </a>
        
        <div class="security-features">
            <h3><i class="fas fa-shield-alt"></i> Sécurité</h3>
            <div class="security-item">
                <i class="fas fa-check"></i>
                <span>Connexion sécurisée</span>
            </div>
            <div class="security-item">
                <i class="fas fa-check"></i>
                <span>Session temporaire</span>
            </div>
            <div class="security-item">
                <i class="fas fa-check"></i>
                <span>Accès restreint</span>
            </div>
        </div>
    </div>

    <script>
        // Gestion de la connexion
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Identifiants de connexion (vous pouvez les changer)
            const validUsername = 'admin';
            const validPassword = 'arwa2024';
            
            if (username === validUsername && password === validPassword) {
                // Créer une session
                sessionStorage.setItem('arwa_admin_logged', 'true');
                sessionStorage.setItem('arwa_admin_time', Date.now());
                
                // Rediriger vers l'administration
                window.location.href = 'admin.html';
            } else {
                // Afficher l'erreur
                document.getElementById('errorMessage').style.display = 'block';
                
                // Masquer l'erreur après 3 secondes
                setTimeout(() => {
                    document.getElementById('errorMessage').style.display = 'none';
                }, 3000);
                
                // Vider les champs
                document.getElementById('password').value = '';
            }
        });
        
        // Vérifier si déjà connecté
        if (sessionStorage.getItem('arwa_admin_logged') === 'true') {
            const loginTime = sessionStorage.getItem('arwa_admin_time');
            const currentTime = Date.now();
            const sessionDuration = 2 * 60 * 60 * 1000; // 2 heures
            
            if (currentTime - loginTime < sessionDuration) {
                // Session encore valide, rediriger
                window.location.href = 'admin.html';
            } else {
                // Session expirée, nettoyer
                sessionStorage.removeItem('arwa_admin_logged');
                sessionStorage.removeItem('arwa_admin_time');
            }
        }
    </script>
</body>
</html>
