<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Administration - ARWA SHOP (v2.9)</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #d63384 0%, #e91e63 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .admin-nav {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
        }
        
        .admin-nav button {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: white;
            color: #d63384;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .admin-nav button.active,
        .admin-nav button:hover {
            background: #d63384;
            color: white;
            transform: translateY(-2px);
        }
        
        .admin-section {
            display: none;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .admin-section.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #d63384;
        }
        
        .colors-input {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .color-tag {
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 15px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .color-tag button {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            table-layout: fixed; /* Force une largeur stable */
        }

        .products-table th,
        .products-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .products-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* Largeurs fixes pour les colonnes */
        .products-table th:nth-child(1) { width: 60px; }   /* ID */
        .products-table th:nth-child(2) { width: 80px; }   /* Image */
        .products-table th:nth-child(3) { width: 200px; }  /* Nom */
        .products-table th:nth-child(4) { width: 100px; }  /* Prix */
        .products-table th:nth-child(5) { width: 120px; }  /* Catégorie */
        .products-table th:nth-child(6) { width: 180px; }  /* Actions */

        .table-row-stable {
            transition: all 0.3s ease;
            animation: fadeInRow 0.5s ease forwards;
        }

        @keyframes fadeInRow {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .products-table img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-edit {
            background: #ffc107;
            color: #000;
            border: none;
            padding: 6px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-save {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            margin-top: 1rem;
        }
        
        .btn-cancel {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            margin-top: 1rem;
            margin-left: 1rem;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #ffeef8 0%, #f8e8f5 100%);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #d63384;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .back-to-site {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d63384;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-to-site:hover {
            background: #e91e63;
            transform: translateY(-2px);
        }

        .logout-btn {
            position: fixed;
            top: 20px;
            right: 180px;
            background: #dc3545;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .admin-user {
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #333;
            border: 1px solid #dee2e6;
        }

        /* Styles pour le tableau de bord */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #d63384;
        }

        .card-header h3 {
            color: #333;
            font-size: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .big-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #d63384;
            margin-bottom: 0.5rem;
        }

        .trend {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .trend.positive { color: #28a745; }
        .trend.negative { color: #dc3545; }
        .trend.neutral { color: #6c757d; }

        .dashboard-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chart-card h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .product-stat, .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f1f1;
        }

        .sales-count {
            background: #e7f3ff;
            color: #0c5460;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .order-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .order-status.pending { background: #fff3cd; color: #856404; }
        .order-status.shipped { background: #d1ecf1; color: #0c5460; }
        .order-status.delivered { background: #d4edda; color: #155724; }
        .order-status.cancelled { background: #f8d7da; color: #721c24; }

        /* Styles pour les onglets */
        .section-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid #f1f1f1;
        }

        .tab-btn {
            padding: 12px 20px;
            border: none;
            background: transparent;
            color: #666;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active,
        .tab-btn:hover {
            color: #d63384;
            border-bottom-color: #d63384;
        }

        .sub-section {
            display: none;
        }

        .sub-section.active {
            display: block;
        }

        /* Styles pour les tableaux */
        .orders-table, .customers-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .orders-table th, .customers-table th,
        .orders-table td, .customers-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f1f1f1;
        }

        .orders-table th, .customers-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .btn-view {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-right: 0.5rem;
        }

        .status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status.active { background: #d4edda; color: #155724; }
        .status.inactive { background: #f8d7da; color: #721c24; }

        /* Styles pour la gestion des catégories */
        .categories-header {
            margin-bottom: 2rem;
            text-align: right;
        }

        .category-form-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #d63384;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #d63384;
        }

        .category-card.inactive {
            opacity: 0.7;
            border-left-color: #6c757d;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .category-header h3 {
            color: #333;
            margin: 0;
            font-size: 1.2rem;
        }

        .category-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-edit-small, .btn-delete-small {
            padding: 6px 8px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .btn-edit-small {
            background: #17a2b8;
            color: white;
        }

        .btn-edit-small:hover {
            background: #138496;
        }

        .btn-delete-small {
            background: #dc3545;
            color: white;
        }

        .btn-delete-small:hover {
            background: #c82333;
        }

        .category-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .category-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .category-slug {
            background: #f8f9fa;
            color: #495057;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-family: monospace;
        }

        .category-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .category-status.active {
            background: #d4edda;
            color: #155724;
        }

        .category-status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .category-stats {
            text-align: center;
            padding-top: 1rem;
            border-top: 1px solid #f1f1f1;
        }

        .product-count {
            background: #e7f3ff;
            color: #0c5460;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* Styles pour la gestion des promotions */
        .promotions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .promo-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            text-align: center;
            min-width: 120px;
        }

        .stat-card h3 {
            font-size: 0.9rem;
            color: #666;
            margin: 0 0 0.5rem 0;
        }

        .promo-form-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #d63384;
        }

        .promotions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .promo-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #d63384;
            position: relative;
        }

        .promo-card.flash {
            border-left-color: #ff6b35;
            background: linear-gradient(135deg, #fff 0%, #fff8f0 100%);
        }

        .promo-card.inactive {
            opacity: 0.6;
            border-left-color: #6c757d;
        }

        .promo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .promo-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .promo-title {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .promo-title h3 {
            color: #333;
            margin: 0;
            font-size: 1.1rem;
        }

        .flash-badge {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 700;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .promo-code {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .code-label {
            font-size: 0.9rem;
            color: #666;
        }

        .code-value {
            font-family: monospace;
            font-weight: 700;
            font-size: 1.1rem;
            color: #d63384;
            flex: 1;
        }

        .btn-copy {
            background: #6c757d;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .btn-copy:hover {
            background: #5a6268;
        }

        .promo-discount {
            text-align: center;
            margin-bottom: 1rem;
        }

        .discount-value {
            font-size: 2rem;
            font-weight: 700;
            color: #d63384;
            background: linear-gradient(135deg, #d63384, #e91e63);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .promo-dates {
            margin-bottom: 1rem;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .promo-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }

        .promo-status.valid {
            background: #d4edda;
            color: #155724;
        }

        .promo-status.expired {
            background: #f8d7da;
            color: #721c24;
        }

        .promo-usage {
            margin-bottom: 1rem;
        }

        .usage-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .usage-progress {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }

        .usage-text {
            font-size: 0.9rem;
            color: #666;
            text-align: center;
        }

        .promo-min {
            font-size: 0.8rem;
            color: #666;
            text-align: center;
            font-style: italic;
        }

        /* Boutons d'actions rapides */
        .promo-quick-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .btn-flash {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            animation: pulse-glow 2s infinite;
        }

        .btn-flash:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 107, 53, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 107, 53, 0.8); }
        }

        .btn-test {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-test:hover {
            background: #138496;
            transform: translateY(-2px);
        }

        .btn-toggle {
            padding: 6px 8px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            margin-right: 4px;
        }

        .btn-toggle.active {
            background: #28a745;
            color: white;
        }

        .btn-toggle.inactive {
            background: #6c757d;
            color: white;
        }

        .btn-flash-toggle {
            padding: 6px 8px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            margin-right: 4px;
        }

        .btn-flash-toggle.flash-on {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            animation: pulse 1s infinite;
        }

        .btn-flash-toggle.flash-off {
            background: #e9ecef;
            color: #6c757d;
        }

        .flash-count {
            color: #ff6b35 !important;
            animation: pulse 2s infinite;
        }

        /* Notifications */
        .promo-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border-left: 4px solid #28a745;
            z-index: 1000;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease;
            max-width: 400px;
        }

        .promo-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .notification-content i {
            color: #28a745;
            font-size: 1.2rem;
        }

        .btn-copy-notification {
            background: #d63384;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: auto;
        }

        .btn-copy-notification:hover {
            background: #b02a5b;
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-to-site">
        <i class="fas fa-arrow-left"></i> Retour au site
    </a>

    <button onclick="logout()" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i> Déconnexion
    </button>

    <div class="admin-user">
        <i class="fas fa-user"></i> Connecté en tant qu'admin
    </div>

    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-cog"></i> Administration ARWA SHOP</h1>
            <p>Gérez vos produits facilement</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalProducts">0</div>
                <div class="stat-label">Produits total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRobes">0</div>
                <div class="stat-label">Robes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTops">0</div>
                <div class="stat-label">Tops</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalJupes">0</div>
                <div class="stat-label">Jupes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPantalons">0</div>
                <div class="stat-label">Pantalons</div>
            </div>
        </div>

        <div class="admin-nav">
            <button onclick="showSection('dashboard')" class="active" id="dashboardBtn">
                <i class="fas fa-chart-line"></i> Tableau de bord
            </button>
            <button onclick="showSection('products')" id="productsBtn">
                <i class="fas fa-box"></i> Produits
            </button>
            <button onclick="showSection('categories')" id="categoriesBtn">
                <i class="fas fa-tags"></i> Catégories
            </button>
            <button onclick="showSection('orders')" id="ordersBtn">
                <i class="fas fa-shopping-cart"></i> Commandes
            </button>
            <button onclick="showSection('customers')" id="customersBtn">
                <i class="fas fa-users"></i> Clients
            </button>

            <button onclick="showSection('promotions')" id="promotionsBtn">
                <i class="fas fa-percent"></i> Promotions
            </button>
        </div>

        <!-- Section Tableau de bord -->
        <div id="dashboardSection" class="admin-section active">
            <h2><i class="fas fa-chart-line"></i> Tableau de bord</h2>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> Ventes du mois</h3>
                    </div>
                    <div class="card-content">
                        <div class="big-number">1,247 DH</div>
                        <div class="trend positive">+15% ce mois</div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-shopping-bag"></i> Commandes</h3>
                    </div>
                    <div class="card-content">
                        <div class="big-number">23</div>
                        <div class="trend positive">+3 aujourd'hui</div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-users"></i> Nouveaux clients</h3>
                    </div>
                    <div class="card-content">
                        <div class="big-number">12</div>
                        <div class="trend neutral">Cette semaine</div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-eye"></i> Visiteurs</h3>
                    </div>
                    <div class="card-content">
                        <div class="big-number">456</div>
                        <div class="trend positive">+8% cette semaine</div>
                    </div>
                </div>
            </div>

            <div class="dashboard-charts">
                <div class="chart-card">
                    <h3>Produits les plus vendus</h3>
                    <div class="top-products">
                        <div class="product-stat">
                            <span>Robe d'été florale</span>
                            <span class="sales-count">15 ventes</span>
                        </div>
                        <div class="product-stat">
                            <span>Blouse élégante</span>
                            <span class="sales-count">12 ventes</span>
                        </div>
                        <div class="product-stat">
                            <span>Jupe midi chic</span>
                            <span class="sales-count">8 ventes</span>
                        </div>
                    </div>
                </div>

                <div class="chart-card">
                    <h3>Commandes récentes</h3>
                    <div class="recent-orders">
                        <div class="order-item">
                            <span>Commande #1001</span>
                            <span class="order-status pending">En cours</span>
                        </div>
                        <div class="order-item">
                            <span>Commande #1002</span>
                            <span class="order-status shipped">Expédiée</span>
                        </div>
                        <div class="order-item">
                            <span>Commande #1003</span>
                            <span class="order-status delivered">Livrée</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Produits -->
        <div id="productsSection" class="admin-section">
            <div class="section-tabs">
                <button onclick="showProductTab('list')" class="tab-btn active" id="productListTab">
                    <i class="fas fa-list"></i> Liste des produits
                </button>
                <button onclick="showProductTab('add')" id="productAddTab">
                    <i class="fas fa-plus"></i> Ajouter un produit
                </button>
            </div>

            <!-- Sous-section Liste des produits -->
            <div id="productListSubSection" class="sub-section active">
                <div class="success-message" id="listSuccessMessage"></div>
                <div class="error-message" id="listErrorMessage"></div>

                <table class="products-table" id="productsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Image</th>
                            <th>Nom</th>
                            <th>Prix (DH)</th>
                            <th>Catégorie</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- Les produits seront affichés ici -->
                    </tbody>
                </table>
            </div>

            <!-- Sous-section Ajouter produit -->
            <div id="productAddSubSection" class="sub-section">
                <h3 id="formTitle"><i class="fas fa-plus"></i> Ajouter un nouveau produit</h3>
                <div class="success-message" id="formSuccessMessage"></div>
                <div class="error-message" id="formErrorMessage"></div>

                <form id="productForm">
                    <input type="hidden" id="productId" value="">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="productName">Nom du produit *</label>
                            <input type="text" id="productName" required>
                        </div>

                        <div class="form-group">
                            <label for="productPrice">Prix (DH) *</label>
                            <input type="number" id="productPrice" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="productCategory">Catégorie *</label>
                            <select id="productCategory" required>
                                <option value="">Choisir une catégorie</option>
                                <option value="robes">Robes</option>
                                <option value="tops">Tops & Blouses</option>
                                <option value="jupes">Jupes</option>
                                <option value="pantalons">Pantalons</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="productImage">URL de l'image *</label>
                            <input type="text" id="productImage" required placeholder="https://... ou images/nom-fichier.jpg">
                            <small style="color: #666; font-size: 0.9rem;">
                                Exemples : https://images.unsplash.com/... ou images/produit.jpg
                            </small>
                        </div>


                    </div>

                    <div class="form-group">
                        <label for="productDescription">Description *</label>
                        <textarea id="productDescription" rows="3" required></textarea>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>Tailles disponibles</label>
                            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                                <label><input type="checkbox" value="XS" class="size-checkbox"> XS</label>
                                <label><input type="checkbox" value="S" class="size-checkbox"> S</label>
                                <label><input type="checkbox" value="M" class="size-checkbox" checked> M</label>
                                <label><input type="checkbox" value="L" class="size-checkbox"> L</label>
                                <label><input type="checkbox" value="XL" class="size-checkbox"> XL</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="newColor">Couleurs disponibles</label>
                            <div style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
                                <input type="text" id="newColor" placeholder="Ajouter une couleur">
                                <button type="button" onclick="addColor()" class="btn-save" style="margin: 0; padding: 8px 16px;">+</button>
                            </div>
                            <div id="colorsContainer" class="colors-input">
                                <!-- Les couleurs seront affichées ici -->
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button type="submit" class="btn-save">
                            <i class="fas fa-save"></i> Enregistrer le produit
                        </button>
                        <button type="button" onclick="cancelEdit()" class="btn-cancel">
                            <i class="fas fa-times"></i> Annuler
                        </button>
                    </div>
                </form>
            </div>


        </div>

        <!-- Section Catégories -->
        <div id="categoriesSection" class="admin-section">
            <h2><i class="fas fa-tags"></i> Gestion des catégories</h2>
            <div id="categoriesContainer">
                <!-- Le contenu sera généré par JavaScript -->
            </div>
        </div>

        <!-- Section Commandes -->
        <div id="ordersSection" class="admin-section">
            <h2><i class="fas fa-shopping-cart"></i> Gestion des commandes</h2>
            <div id="ordersContainer">
                <!-- Le contenu sera généré par JavaScript -->
            </div>
        </div>

        <!-- Section Clients -->
        <div id="customersSection" class="admin-section">
            <h2><i class="fas fa-users"></i> Gestion des clients</h2>
            <div id="customersContainer">
                <!-- Le contenu sera généré par JavaScript -->
            </div>
        </div>



        <!-- Section Promotions -->
        <div id="promotionsSection" class="admin-section">
            <h2><i class="fas fa-percent"></i> Gestion des promotions</h2>
            <div id="promotionsContainer">
                <!-- Le contenu sera généré par JavaScript -->
            </div>
        </div>

        <!-- Section Ajouter/Modifier produit -->
        <div id="addSection" class="admin-section">
            <h2 id="formTitle"><i class="fas fa-plus"></i> Ajouter un nouveau produit</h2>
            <div class="success-message" id="formSuccessMessage"></div>
            <div class="error-message" id="formErrorMessage"></div>
            
            <form id="productForm">
                <input type="hidden" id="productId" value="">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="productName">Nom du produit *</label>
                        <input type="text" id="productName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="productPrice">Prix (DH) *</label>
                        <input type="number" id="productPrice" min="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="productCategory">Catégorie *</label>
                        <select id="productCategory" required>
                            <option value="">Choisir une catégorie</option>
                            <option value="robes">Robes</option>
                            <option value="tops">Tops & Blouses</option>
                            <option value="jupes">Jupes</option>
                            <option value="pantalons">Pantalons</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="productImage">URL de l'image *</label>
                        <input type="text" id="productImage" required placeholder="https://... ou images/nom-fichier.jpg">
                        <small style="color: #666; font-size: 0.9rem;">
                            Exemples : https://images.unsplash.com/... ou images/produit.jpg
                        </small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="productDescription">Description *</label>
                    <textarea id="productDescription" rows="3" required></textarea>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label>Tailles disponibles</label>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <label><input type="checkbox" value="XS" class="size-checkbox"> XS</label>
                            <label><input type="checkbox" value="S" class="size-checkbox"> S</label>
                            <label><input type="checkbox" value="M" class="size-checkbox" checked> M</label>
                            <label><input type="checkbox" value="L" class="size-checkbox"> L</label>
                            <label><input type="checkbox" value="XL" class="size-checkbox"> XL</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="newColor">Couleurs disponibles</label>
                        <div style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
                            <input type="text" id="newColor" placeholder="Ajouter une couleur">
                            <button type="button" onclick="addColor()" class="btn-save" style="margin: 0; padding: 8px 16px;">+</button>
                        </div>
                        <div id="colorsContainer" class="colors-input">
                            <!-- Les couleurs seront affichées ici -->
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i> Enregistrer le produit
                    </button>
                    <button type="button" onclick="cancelEdit()" class="btn-cancel">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts mis à jour - Version sans gestion de stock -->
    <script src="script.js?v=2.9"></script>
    <script src="admin.js?v=2.9"></script>
    <script src="admin-extended.js?v=2.9"></script>
</body>
</html>
