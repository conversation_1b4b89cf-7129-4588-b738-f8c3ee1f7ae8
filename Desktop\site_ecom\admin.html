<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - ARWA SHOP</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #d63384 0%, #e91e63 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .admin-nav {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
        }
        
        .admin-nav button {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: white;
            color: #d63384;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .admin-nav button.active,
        .admin-nav button:hover {
            background: #d63384;
            color: white;
            transform: translateY(-2px);
        }
        
        .admin-section {
            display: none;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .admin-section.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #d63384;
        }
        
        .colors-input {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .color-tag {
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 15px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .color-tag button {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .products-table th,
        .products-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .products-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .products-table img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-edit {
            background: #ffc107;
            color: #000;
            border: none;
            padding: 6px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-save {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            margin-top: 1rem;
        }
        
        .btn-cancel {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            margin-top: 1rem;
            margin-left: 1rem;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #ffeef8 0%, #f8e8f5 100%);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #d63384;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .back-to-site {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d63384;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-to-site:hover {
            background: #e91e63;
            transform: translateY(-2px);
        }

        .logout-btn {
            position: fixed;
            top: 20px;
            right: 180px;
            background: #dc3545;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .admin-user {
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #333;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-to-site">
        <i class="fas fa-arrow-left"></i> Retour au site
    </a>

    <button onclick="logout()" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i> Déconnexion
    </button>

    <div class="admin-user">
        <i class="fas fa-user"></i> Connecté en tant qu'admin
    </div>

    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-cog"></i> Administration ARWA SHOP</h1>
            <p>Gérez vos produits facilement</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalProducts">0</div>
                <div class="stat-label">Produits total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRobes">0</div>
                <div class="stat-label">Robes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTops">0</div>
                <div class="stat-label">Tops</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalJupes">0</div>
                <div class="stat-label">Jupes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPantalons">0</div>
                <div class="stat-label">Pantalons</div>
            </div>
        </div>

        <div class="admin-nav">
            <button onclick="showSection('list')" class="active" id="listBtn">
                <i class="fas fa-list"></i> Liste des produits
            </button>
            <button onclick="showSection('add')" id="addBtn">
                <i class="fas fa-plus"></i> Ajouter un produit
            </button>
        </div>

        <!-- Section Liste des produits -->
        <div id="listSection" class="admin-section active">
            <h2><i class="fas fa-list"></i> Liste des produits</h2>
            <div class="success-message" id="listSuccessMessage"></div>
            <div class="error-message" id="listErrorMessage"></div>
            
            <table class="products-table" id="productsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Image</th>
                        <th>Nom</th>
                        <th>Prix (DH)</th>
                        <th>Catégorie</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="productsTableBody">
                    <!-- Les produits seront affichés ici -->
                </tbody>
            </table>
        </div>

        <!-- Section Ajouter/Modifier produit -->
        <div id="addSection" class="admin-section">
            <h2 id="formTitle"><i class="fas fa-plus"></i> Ajouter un nouveau produit</h2>
            <div class="success-message" id="formSuccessMessage"></div>
            <div class="error-message" id="formErrorMessage"></div>
            
            <form id="productForm">
                <input type="hidden" id="productId" value="">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="productName">Nom du produit *</label>
                        <input type="text" id="productName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="productPrice">Prix (DH) *</label>
                        <input type="number" id="productPrice" min="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="productCategory">Catégorie *</label>
                        <select id="productCategory" required>
                            <option value="">Choisir une catégorie</option>
                            <option value="robes">Robes</option>
                            <option value="tops">Tops & Blouses</option>
                            <option value="jupes">Jupes</option>
                            <option value="pantalons">Pantalons</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="productImage">URL de l'image *</label>
                        <input type="text" id="productImage" required placeholder="https://... ou images/nom-fichier.jpg">
                        <small style="color: #666; font-size: 0.9rem;">
                            Exemples : https://images.unsplash.com/... ou images/produit.jpg
                        </small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="productDescription">Description *</label>
                    <textarea id="productDescription" rows="3" required></textarea>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label>Tailles disponibles</label>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <label><input type="checkbox" value="XS" class="size-checkbox"> XS</label>
                            <label><input type="checkbox" value="S" class="size-checkbox"> S</label>
                            <label><input type="checkbox" value="M" class="size-checkbox" checked> M</label>
                            <label><input type="checkbox" value="L" class="size-checkbox"> L</label>
                            <label><input type="checkbox" value="XL" class="size-checkbox"> XL</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="newColor">Couleurs disponibles</label>
                        <div style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
                            <input type="text" id="newColor" placeholder="Ajouter une couleur">
                            <button type="button" onclick="addColor()" class="btn-save" style="margin: 0; padding: 8px 16px;">+</button>
                        </div>
                        <div id="colorsContainer" class="colors-input">
                            <!-- Les couleurs seront affichées ici -->
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i> Enregistrer le produit
                    </button>
                    <button type="button" onclick="cancelEdit()" class="btn-cancel">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="admin.js"></script>
</body>
</html>
