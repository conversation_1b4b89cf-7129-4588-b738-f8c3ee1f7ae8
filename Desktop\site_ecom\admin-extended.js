// Extension de l'administration ARWA SHOP
// Données simulées pour les nouvelles fonctionnalités

// Base de données des commandes
let orders = [
    {
        id: 1001,
        customer: "Fatima El Amrani",
        email: "<EMAIL>",
        phone: "+212 6 12 34 56 78",
        products: [
            { id: 1, name: "Robe d'été florale", price: 499, quantity: 1 }
        ],
        total: 499,
        status: "pending", // pending, shipped, delivered, cancelled
        date: "2024-01-15",
        address: "123 Rue Hassan II, Casablanca"
    },
    {
        id: 1002,
        customer: "<PERSON><PERSON>",
        email: "<EMAIL>",
        phone: "+212 6 87 65 43 21",
        products: [
            { id: 2, name: "Blouse élégante", price: 399, quantity: 2 }
        ],
        total: 798,
        status: "shipped",
        date: "2024-01-14",
        address: "456 Avenue Mohammed V, Rabat"
    },
    {
        id: 1003,
        customer: "<PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        phone: "+212 6 11 22 33 44",
        products: [
            { id: 1, name: "<PERSON>e d'été florale", price: 499, quantity: 1 },
            { id: 3, name: "<PERSON><PERSON> midi chic", price: 349, quantity: 1 }
        ],
        total: 848,
        status: "delivered",
        date: "2024-01-13",
        address: "789 Boulevard Zerktouni, Marrakech"
    }
];

// Base de données des clients
let customers = [
    {
        id: 1,
        name: "Fatima El Amrani",
        email: "<EMAIL>",
        phone: "+212 6 12 34 56 78",
        address: "123 Rue Hassan II, Casablanca",
        orders: 3,
        totalSpent: 1247,
        joinDate: "2023-12-01",
        status: "active"
    },
    {
        id: 2,
        name: "Aicha Benali",
        email: "<EMAIL>",
        phone: "+212 6 87 65 43 21",
        address: "456 Avenue Mohammed V, Rabat",
        orders: 2,
        totalSpent: 798,
        joinDate: "2024-01-05",
        status: "active"
    },
    {
        id: 3,
        name: "Khadija Alami",
        email: "<EMAIL>",
        phone: "+212 6 11 22 33 44",
        address: "789 Boulevard Zerktouni, Marrakech",
        orders: 1,
        totalSpent: 848,
        joinDate: "2024-01-10",
        status: "active"
    }
];

// Base de données des catégories
let categories = [
    { id: 1, name: "Robes", slug: "robes", description: "Collection de robes élégantes", active: true },
    { id: 2, name: "Tops & Blouses", slug: "tops", description: "Hauts et blouses tendance", active: true },
    { id: 3, name: "Jupes", slug: "jupes", description: "Jupes de toutes longueurs", active: true },
    { id: 4, name: "Pantalons", slug: "pantalons", description: "Pantalons et jeans", active: true },
    { id: 5, name: "Accessoires", slug: "accessoires", description: "Sacs, bijoux et accessoires", active: false }
];

// Base de données des promotions
let promotions = [
    {
        id: 1,
        name: "Promo Printemps",
        code: "SPRING20",
        discount: 20,
        type: "percentage", // percentage, fixed
        active: true,
        startDate: "2024-01-01",
        endDate: "2024-03-31",
        usageCount: 15,
        maxUsage: 100
    },
    {
        id: 2,
        name: "Bienvenue",
        code: "WELCOME10",
        discount: 10,
        type: "percentage",
        active: true,
        startDate: "2024-01-01",
        endDate: "2024-12-31",
        usageCount: 8,
        maxUsage: 50
    }
];

// Contenu du site
let siteContent = {
    homepage: {
        heroTitle: "Collection Printemps 2024",
        heroSubtitle: "Découvrez notre nouvelle collection de vêtements féminins",
        promoText: "🌸 -20% sur votre première commande avec le code SPRING20"
    },
    about: {
        title: "À propos d'ARWA SHOP",
        description: "Nous sommes passionnés par la mode féminine et nous nous engageons à offrir des vêtements de qualité qui mettent en valeur votre beauté naturelle."
    },
    contact: {
        address: "123 Avenue Mohammed V, Casablanca, Maroc",
        phone: "+212 5 22 12 34 56",
        email: "<EMAIL>"
    }
};

// Fonctions pour la gestion des sections
function showSection(section) {
    // Masquer toutes les sections
    document.querySelectorAll('.admin-section').forEach(s => s.classList.remove('active'));
    document.querySelectorAll('.admin-nav button').forEach(b => b.classList.remove('active'));
    
    // Afficher la section demandée
    document.getElementById(section + 'Section').classList.add('active');
    document.getElementById(section + 'Btn').classList.add('active');
    
    // Charger le contenu spécifique
    switch(section) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'products':
            // Section produits - formulaire d'ajout uniquement
            break;
        case 'categories':
            loadCategories();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'customers':
            loadCustomers();
            break;
        case 'content':
            loadContent();
            break;
        case 'promotions':
            loadPromotions();
            break;
    }
}

// Section produits simplifiée - formulaire d'ajout uniquement

// Charger le tableau de bord
function loadDashboard() {
    // Les données sont déjà affichées dans le HTML
    // Ici on pourrait ajouter des graphiques dynamiques
    console.log('Dashboard chargé');
}

// Charger la gestion des commandes
function loadOrders() {
    const container = document.getElementById('ordersContainer');
    if (!container) return;
    
    container.innerHTML = `
        <div class="orders-filters">
            <select onchange="filterOrders(this.value)">
                <option value="all">Toutes les commandes</option>
                <option value="pending">En cours</option>
                <option value="shipped">Expédiées</option>
                <option value="delivered">Livrées</option>
                <option value="cancelled">Annulées</option>
            </select>
        </div>
        <table class="orders-table">
            <thead>
                <tr>
                    <th>N° Commande</th>
                    <th>Client</th>
                    <th>Date</th>
                    <th>Total</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="ordersTableBody">
                ${orders.map(order => `
                    <tr>
                        <td>#${order.id}</td>
                        <td>${order.customer}</td>
                        <td>${formatDate(order.date)}</td>
                        <td>${order.total} DH</td>
                        <td><span class="status ${order.status}">${getStatusText(order.status)}</span></td>
                        <td>
                            <button onclick="viewOrder(${order.id})" class="btn-view">Voir</button>
                            <button onclick="updateOrderStatus(${order.id})" class="btn-edit">Modifier</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

// Charger la gestion des clients
function loadCustomers() {
    const container = document.getElementById('customersContainer');
    if (!container) return;
    
    container.innerHTML = `
        <table class="customers-table">
            <thead>
                <tr>
                    <th>Nom</th>
                    <th>Email</th>
                    <th>Téléphone</th>
                    <th>Commandes</th>
                    <th>Total dépensé</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${customers.map(customer => `
                    <tr>
                        <td>${customer.name}</td>
                        <td>${customer.email}</td>
                        <td>${customer.phone}</td>
                        <td>${customer.orders}</td>
                        <td>${customer.totalSpent} DH</td>
                        <td><span class="status ${customer.status}">${customer.status}</span></td>
                        <td>
                            <button onclick="viewCustomer(${customer.id})" class="btn-view">Voir</button>
                            <button onclick="blockCustomer(${customer.id})" class="btn-delete">Bloquer</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

// Fonctions utilitaires
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

function getStatusText(status) {
    const statusTexts = {
        'pending': 'En cours',
        'shipped': 'Expédiée',
        'delivered': 'Livrée',
        'cancelled': 'Annulée'
    };
    return statusTexts[status] || status;
}

// Sauvegarder toutes les données
function saveAllData() {
    localStorage.setItem('arwa_orders', JSON.stringify(orders));
    localStorage.setItem('arwa_customers', JSON.stringify(customers));
    localStorage.setItem('arwa_categories', JSON.stringify(categories));
    localStorage.setItem('arwa_promotions', JSON.stringify(promotions));
    localStorage.setItem('arwa_content', JSON.stringify(siteContent));
}

// Charger toutes les données
function loadAllData() {
    const savedOrders = localStorage.getItem('arwa_orders');
    if (savedOrders) orders = JSON.parse(savedOrders);
    
    const savedCustomers = localStorage.getItem('arwa_customers');
    if (savedCustomers) customers = JSON.parse(savedCustomers);
    
    const savedCategories = localStorage.getItem('arwa_categories');
    if (savedCategories) categories = JSON.parse(savedCategories);
    
    const savedPromotions = localStorage.getItem('arwa_promotions');
    if (savedPromotions) promotions = JSON.parse(savedPromotions);
    
    const savedContent = localStorage.getItem('arwa_content');
    if (savedContent) siteContent = JSON.parse(savedContent);
}

// Initialiser au chargement
document.addEventListener('DOMContentLoaded', function() {
    loadAllData();
});
