// Extension de l'administration ARWA SHOP
// Données simulées pour les nouvelles fonctionnalités

// Base de données des commandes
let orders = [
    {
        id: 1001,
        customer: "Fatima El Amrani",
        email: "<EMAIL>",
        phone: "+212 6 12 34 56 78",
        products: [
            { id: 1, name: "Robe d'été florale", price: 499, quantity: 1 }
        ],
        total: 499,
        status: "pending", // pending, shipped, delivered, cancelled
        date: "2024-01-15",
        address: "123 Rue Hassan II, Casablanca"
    },
    {
        id: 1002,
        customer: "<PERSON><PERSON>",
        email: "<EMAIL>",
        phone: "+212 6 87 65 43 21",
        products: [
            { id: 2, name: "Blouse élégante", price: 399, quantity: 2 }
        ],
        total: 798,
        status: "shipped",
        date: "2024-01-14",
        address: "456 Avenue Mohammed V, Rabat"
    },
    {
        id: 1003,
        customer: "<PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        phone: "+212 6 11 22 33 44",
        products: [
            { id: 1, name: "<PERSON>e d'été florale", price: 499, quantity: 1 },
            { id: 3, name: "<PERSON><PERSON> midi chic", price: 349, quantity: 1 }
        ],
        total: 848,
        status: "delivered",
        date: "2024-01-13",
        address: "789 Boulevard Zerktouni, Marrakech"
    }
];

// Base de données des clients
let customers = [
    {
        id: 1,
        name: "Fatima El Amrani",
        email: "<EMAIL>",
        phone: "+212 6 12 34 56 78",
        address: "123 Rue Hassan II, Casablanca",
        orders: 3,
        totalSpent: 1247,
        joinDate: "2023-12-01",
        status: "active"
    },
    {
        id: 2,
        name: "Aicha Benali",
        email: "<EMAIL>",
        phone: "+212 6 87 65 43 21",
        address: "456 Avenue Mohammed V, Rabat",
        orders: 2,
        totalSpent: 798,
        joinDate: "2024-01-05",
        status: "active"
    },
    {
        id: 3,
        name: "Khadija Alami",
        email: "<EMAIL>",
        phone: "+212 6 11 22 33 44",
        address: "789 Boulevard Zerktouni, Marrakech",
        orders: 1,
        totalSpent: 848,
        joinDate: "2024-01-10",
        status: "active"
    }
];

// Base de données des catégories
let categories = [
    { id: 1, name: "Robes", slug: "robes", description: "Collection de robes élégantes pour toutes occasions", type: "type", active: true },
    { id: 2, name: "Tops & Blouses", slug: "tops", description: "Hauts et blouses tendance", type: "type", active: true },
    { id: 3, name: "Jupes", slug: "jupes", description: "Jupes de toutes longueurs", type: "type", active: true },
    { id: 4, name: "Pantalons", slug: "pantalons", description: "Pantalons et jeans", type: "type", active: true },
    { id: 5, name: "Accessoires", slug: "accessoires", description: "Sacs, bijoux et accessoires", type: "type", active: true },
    { id: 6, name: "Collection Printemps", slug: "printemps", description: "Nouveautés de la saison printemps", type: "saison", active: true },
    { id: 7, name: "Collection Été", slug: "ete", description: "Vêtements légers pour l'été", type: "saison", active: true },
    { id: 8, name: "Tenues de Soirée", slug: "soiree", description: "Tenues élégantes pour les occasions spéciales", type: "occasion", active: true },
    { id: 9, name: "Casual Chic", slug: "casual", description: "Style décontracté et élégant", type: "occasion", active: true }
];

// Base de données des promotions
let promotions = [
    {
        id: 1,
        name: "Promo Printemps",
        code: "SPRING20",
        discount: 20,
        type: "percentage",
        active: true,
        startDate: "2024-01-01",
        endDate: "2024-03-31",
        usageCount: 15,
        maxUsage: 100,
        minAmount: 200,
        flash: false
    },
    {
        id: 2,
        name: "Bienvenue Nouveaux Clients",
        code: "WELCOME10",
        discount: 10,
        type: "percentage",
        active: true,
        startDate: "2024-01-01",
        endDate: "2024-12-31",
        usageCount: 8,
        maxUsage: 50,
        minAmount: 0,
        flash: false
    },
    {
        id: 3,
        name: "Flash Sale Weekend",
        code: "FLASH30",
        discount: 30,
        type: "percentage",
        active: true,
        startDate: "2024-07-05",
        endDate: "2024-07-07",
        usageCount: 3,
        maxUsage: 20,
        minAmount: 150,
        flash: true
    },
    {
        id: 4,
        name: "Réduction Fixe Été",
        code: "ETE50",
        discount: 50,
        type: "fixed",
        active: true,
        startDate: "2024-06-01",
        endDate: "2024-08-31",
        usageCount: 12,
        maxUsage: null,
        minAmount: 300,
        flash: false
    },
    {
        id: 5,
        name: "Mega Flash 48H",
        code: "MEGA48",
        discount: 40,
        type: "percentage",
        active: false,
        startDate: "2024-07-10",
        endDate: "2024-07-12",
        usageCount: 0,
        maxUsage: 15,
        minAmount: 100,
        flash: true
    }
];



// Fonctions pour la gestion des sections
function showSection(section) {
    // Masquer toutes les sections
    document.querySelectorAll('.admin-section').forEach(s => s.classList.remove('active'));
    document.querySelectorAll('.admin-nav button').forEach(b => b.classList.remove('active'));
    
    // Afficher la section demandée
    document.getElementById(section + 'Section').classList.add('active');
    document.getElementById(section + 'Btn').classList.add('active');

    // Gestion spéciale pour le formulaire d'ajout
    if (section === 'add' && typeof resetForm === 'function') {
        resetForm();
    }

    // Charger le contenu spécifique
    switch(section) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'products':
            showProductTab('list');
            break;
        case 'categories':
            loadCategories();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'customers':
            loadCustomers();
            break;
        case 'promotions':
            loadPromotions();
            break;
    }
}

// Gestion des onglets produits
function showProductTab(tab) {
    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
    document.querySelectorAll('.sub-section').forEach(s => s.classList.remove('active'));

    document.getElementById('product' + tab.charAt(0).toUpperCase() + tab.slice(1) + 'Tab').classList.add('active');
    document.getElementById('product' + tab.charAt(0).toUpperCase() + tab.slice(1) + 'SubSection').classList.add('active');

    if (tab === 'list') {
        loadProductsTable();
    }
}

// Charger le tableau de bord
function loadDashboard() {
    console.log('Chargement du tableau de bord...');

    // Calculer les statistiques en temps réel
    const stats = calculateDashboardStats();

    // Mettre à jour les cartes du dashboard
    updateDashboardCards(stats);

    // Mettre à jour les graphiques
    updateDashboardCharts(stats);

    // Ajouter des animations
    animateDashboardCards();
}

// Calculer les statistiques du dashboard
function calculateDashboardStats() {
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();

    // Statistiques des produits
    const totalProducts = products.length;
    const productsByCategory = {};
    products.forEach(product => {
        productsByCategory[product.category] = (productsByCategory[product.category] || 0) + 1;
    });

    // Statistiques des commandes
    const thisMonthOrders = orders.filter(order => {
        const orderDate = new Date(order.date);
        return orderDate.getMonth() === thisMonth && orderDate.getFullYear() === thisYear;
    });

    const totalRevenue = thisMonthOrders.reduce((sum, order) => sum + order.total, 0);
    const todayOrders = orders.filter(order => {
        const orderDate = new Date(order.date);
        const today = new Date();
        return orderDate.toDateString() === today.toDateString();
    });

    // Statistiques des clients
    const thisWeekCustomers = customers.filter(customer => {
        const customerDate = new Date(customer.registrationDate);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return customerDate >= weekAgo;
    });

    // Statistiques des promotions
    const activePromotions = promotions.filter(p => p.active && isPromoValid(p));
    const flashPromotions = promotions.filter(p => p.flash && p.active);

    // Produits les plus vendus (simulation)
    const topProducts = [
        { name: "Robe d'été florale", sales: Math.floor(Math.random() * 20) + 10 },
        { name: "Blouse élégante", sales: Math.floor(Math.random() * 15) + 8 },
        { name: "Jupe plissée", sales: Math.floor(Math.random() * 12) + 6 },
        { name: "Pantalon chic", sales: Math.floor(Math.random() * 10) + 5 },
        { name: "Accessoire tendance", sales: Math.floor(Math.random() * 8) + 3 }
    ].sort((a, b) => b.sales - a.sales);

    return {
        totalProducts,
        productsByCategory,
        totalRevenue,
        thisMonthOrders: thisMonthOrders.length,
        todayOrders: todayOrders.length,
        thisWeekCustomers: thisWeekCustomers.length,
        activePromotions: activePromotions.length,
        flashPromotions: flashPromotions.length,
        topProducts,
        visitors: Math.floor(Math.random() * 200) + 300 // Simulation
    };
}

// Mettre à jour les cartes du dashboard
function updateDashboardCards(stats) {
    // Carte des ventes
    const salesCard = document.querySelector('.dashboard-card .big-number');
    if (salesCard) {
        salesCard.textContent = `${stats.totalRevenue.toLocaleString()} DH`;
    }

    // Carte des commandes
    const ordersCards = document.querySelectorAll('.dashboard-card .big-number');
    if (ordersCards[1]) {
        ordersCards[1].textContent = stats.thisMonthOrders;
    }

    // Mettre à jour le trend des commandes
    const ordersTrend = document.querySelectorAll('.dashboard-card .trend')[1];
    if (ordersTrend) {
        ordersTrend.textContent = `+${stats.todayOrders} aujourd'hui`;
    }

    // Carte des nouveaux clients
    if (ordersCards[2]) {
        ordersCards[2].textContent = stats.thisWeekCustomers;
    }

    // Carte des visiteurs
    if (ordersCards[3]) {
        ordersCards[3].textContent = stats.visitors;
    }
}

// Mettre à jour les graphiques du dashboard
function updateDashboardCharts(stats) {
    // Mettre à jour la liste des produits les plus vendus
    const topProductsContainer = document.querySelector('.top-products');
    if (topProductsContainer) {
        topProductsContainer.innerHTML = stats.topProducts.map(product => `
            <div class="product-stat">
                <span>${product.name}</span>
                <span class="sales-count">${product.sales} ventes</span>
            </div>
        `).join('');
    }

    // Ajouter un graphique des catégories
    const chartContainer = document.querySelector('.dashboard-charts');
    if (chartContainer && !document.querySelector('.category-chart')) {
        const categoryChart = document.createElement('div');
        categoryChart.className = 'chart-card category-chart';
        categoryChart.innerHTML = `
            <h3>Répartition par catégories</h3>
            <div class="category-stats">
                ${Object.entries(stats.productsByCategory).map(([category, count]) => `
                    <div class="category-stat">
                        <span class="category-name">${category}</span>
                        <div class="category-bar">
                            <div class="category-progress" style="width: ${(count / stats.totalProducts) * 100}%"></div>
                        </div>
                        <span class="category-count">${count}</span>
                    </div>
                `).join('')}
            </div>
        `;
        chartContainer.appendChild(categoryChart);
    }
}

// Animer les cartes du dashboard
function animateDashboardCards() {
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animer les nombres
    const numbers = document.querySelectorAll('.big-number');
    numbers.forEach(number => {
        const finalValue = parseInt(number.textContent.replace(/[^\d]/g, ''));
        if (finalValue) {
            animateNumber(number, 0, finalValue, 1000);
        }
    });
}

// Animer un nombre de 0 à sa valeur finale
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const originalText = element.textContent;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const currentValue = Math.floor(start + (end - start) * progress);

        if (originalText.includes('DH')) {
            element.textContent = `${currentValue.toLocaleString()} DH`;
        } else {
            element.textContent = currentValue;
        }

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

// Charger la gestion des commandes
function loadOrders() {
    const container = document.getElementById('ordersContainer');
    if (!container) return;
    
    container.innerHTML = `
        <div class="orders-filters">
            <select onchange="filterOrders(this.value)">
                <option value="all">Toutes les commandes</option>
                <option value="pending">En cours</option>
                <option value="shipped">Expédiées</option>
                <option value="delivered">Livrées</option>
                <option value="cancelled">Annulées</option>
            </select>
        </div>
        <table class="orders-table">
            <thead>
                <tr>
                    <th>N° Commande</th>
                    <th>Client</th>
                    <th>Date</th>
                    <th>Total</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="ordersTableBody">
                ${orders.map(order => `
                    <tr>
                        <td>#${order.id}</td>
                        <td>${order.customer}</td>
                        <td>${formatDate(order.date)}</td>
                        <td>${order.total} DH</td>
                        <td><span class="status ${order.status}">${getStatusText(order.status)}</span></td>
                        <td>
                            <button onclick="viewOrder(${order.id})" class="btn-view">Voir</button>
                            <button onclick="updateOrderStatus(${order.id})" class="btn-edit">Modifier</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

// Charger la gestion des catégories
function loadCategories() {
    const container = document.getElementById('categoriesContainer');
    if (!container) return;

    container.innerHTML = `
        <div class="categories-header">
            <button onclick="showAddCategoryForm()" class="btn-save">
                <i class="fas fa-plus"></i> Ajouter une catégorie
            </button>
        </div>

        <div id="categoryFormContainer" class="category-form-container" style="display: none;">
            <h3 id="categoryFormTitle">Ajouter une nouvelle catégorie</h3>
            <form id="categoryForm" onsubmit="saveCategoryForm(event)">
                <input type="hidden" id="categoryId" value="">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="categoryName">Nom de la catégorie *</label>
                        <input type="text" id="categoryName" required placeholder="Ex: Robes d'été">
                    </div>
                    <div class="form-group">
                        <label for="categorySlug">Identifiant (slug) *</label>
                        <input type="text" id="categorySlug" required placeholder="Ex: robes-ete">
                        <small>Utilisé dans les URLs (lettres, chiffres, tirets uniquement)</small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="categoryDescription">Description</label>
                    <textarea id="categoryDescription" rows="3" placeholder="Description de la catégorie..."></textarea>
                </div>
                <div class="form-group">
                    <label for="categoryType">Type d'organisation</label>
                    <select id="categoryType">
                        <option value="type">Par type de vêtement</option>
                        <option value="saison">Par saison</option>
                        <option value="taille">Par taille</option>
                        <option value="occasion">Par occasion</option>
                        <option value="collection">Par collection</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="categoryActive" checked> Catégorie active
                    </label>
                </div>
                <div style="text-align: center; margin-top: 1rem;">
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <button type="button" onclick="cancelCategoryForm()" class="btn-cancel">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                </div>
            </form>
        </div>

        <div class="categories-grid">
            ${categories.map(category => `
                <div class="category-card ${category.active ? 'active' : 'inactive'}">
                    <div class="category-header">
                        <h3>${category.name}</h3>
                        <div class="category-actions">
                            <button onclick="editCategory(${category.id})" class="btn-edit-small">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteCategory(${category.id})" class="btn-delete-small">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="category-info">
                        <p class="category-description">${category.description || 'Aucune description'}</p>
                        <div class="category-meta">
                            <span class="category-slug">${category.slug}</span>
                            <span class="category-status ${category.active ? 'active' : 'inactive'}">
                                ${category.active ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                        <div class="category-stats">
                            <span class="product-count">
                                ${getProductCountByCategory(category.slug)} produit(s)
                            </span>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Charger la gestion des clients
function loadCustomers() {
    const container = document.getElementById('customersContainer');
    if (!container) return;

    container.innerHTML = `
        <table class="customers-table">
            <thead>
                <tr>
                    <th>Nom</th>
                    <th>Email</th>
                    <th>Téléphone</th>
                    <th>Commandes</th>
                    <th>Total dépensé</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${customers.map(customer => `
                    <tr>
                        <td>${customer.name}</td>
                        <td>${customer.email}</td>
                        <td>${customer.phone}</td>
                        <td>${customer.orders}</td>
                        <td>${customer.totalSpent} DH</td>
                        <td><span class="status ${customer.status}">${customer.status}</span></td>
                        <td>
                            <button onclick="viewCustomer(${customer.id})" class="btn-view">Voir</button>
                            <button onclick="blockCustomer(${customer.id})" class="btn-delete">Bloquer</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

// Fonctions utilitaires
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

function getStatusText(status) {
    const statusTexts = {
        'pending': 'En cours',
        'shipped': 'Expédiée',
        'delivered': 'Livrée',
        'cancelled': 'Annulée'
    };
    return statusTexts[status] || status;
}

// Sauvegarder toutes les données
function saveAllData() {
    localStorage.setItem('arwa_orders', JSON.stringify(orders));
    localStorage.setItem('arwa_customers', JSON.stringify(customers));
    localStorage.setItem('arwa_categories', JSON.stringify(categories));
    localStorage.setItem('arwa_promotions', JSON.stringify(promotions));
}

// Charger toutes les données
function loadAllData() {
    const savedOrders = localStorage.getItem('arwa_orders');
    if (savedOrders) orders = JSON.parse(savedOrders);
    
    const savedCustomers = localStorage.getItem('arwa_customers');
    if (savedCustomers) customers = JSON.parse(savedCustomers);
    
    const savedCategories = localStorage.getItem('arwa_categories');
    if (savedCategories) categories = JSON.parse(savedCategories);
    
    const savedPromotions = localStorage.getItem('arwa_promotions');
    if (savedPromotions) promotions = JSON.parse(savedPromotions);
    
    const savedContent = localStorage.getItem('arwa_content');
    if (savedContent) siteContent = JSON.parse(savedContent);
}

// Fonctions pour la gestion des catégories
function showAddCategoryForm() {
    document.getElementById('categoryFormContainer').style.display = 'block';
    document.getElementById('categoryFormTitle').textContent = 'Ajouter une nouvelle catégorie';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    document.getElementById('categoryActive').checked = true;
}

function cancelCategoryForm() {
    document.getElementById('categoryFormContainer').style.display = 'none';
    document.getElementById('categoryForm').reset();
}

function saveCategoryForm(event) {
    event.preventDefault();

    const id = document.getElementById('categoryId').value;
    const name = document.getElementById('categoryName').value.trim();
    const slug = document.getElementById('categorySlug').value.trim();
    const description = document.getElementById('categoryDescription').value.trim();
    const type = document.getElementById('categoryType').value;
    const active = document.getElementById('categoryActive').checked;

    if (!name || !slug) {
        alert('Le nom et l\'identifiant sont obligatoires');
        return;
    }

    // Vérifier que le slug est unique
    const existingCategory = categories.find(c => c.slug === slug && c.id != id);
    if (existingCategory) {
        alert('Cet identifiant existe déjà');
        return;
    }

    if (id) {
        // Modification
        const categoryIndex = categories.findIndex(c => c.id == id);
        if (categoryIndex !== -1) {
            categories[categoryIndex] = {
                ...categories[categoryIndex],
                name,
                slug,
                description,
                type,
                active
            };
        }
    } else {
        // Ajout
        const newId = Math.max(...categories.map(c => c.id), 0) + 1;
        categories.push({
            id: newId,
            name,
            slug,
            description,
            type,
            active
        });
    }

    saveAllData();
    loadCategories();
    cancelCategoryForm();

    // Mettre à jour le select des catégories dans le formulaire produit
    updateCategorySelect();
}

function editCategory(id) {
    const category = categories.find(c => c.id === id);
    if (!category) return;

    document.getElementById('categoryFormContainer').style.display = 'block';
    document.getElementById('categoryFormTitle').textContent = 'Modifier la catégorie';
    document.getElementById('categoryId').value = category.id;
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categorySlug').value = category.slug;
    document.getElementById('categoryDescription').value = category.description || '';
    document.getElementById('categoryType').value = category.type || 'type';
    document.getElementById('categoryActive').checked = category.active;
}

function deleteCategory(id) {
    const category = categories.find(c => c.id === id);
    if (!category) return;

    // Vérifier s'il y a des produits dans cette catégorie
    const productCount = getProductCountByCategory(category.slug);
    if (productCount > 0) {
        if (!confirm(`Cette catégorie contient ${productCount} produit(s). Êtes-vous sûr de vouloir la supprimer ?`)) {
            return;
        }
    } else {
        if (!confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${category.name}" ?`)) {
            return;
        }
    }

    categories = categories.filter(c => c.id !== id);
    saveAllData();
    loadCategories();
    updateCategorySelect();
}

function getProductCountByCategory(categorySlug) {
    return products.filter(p => p.category === categorySlug).length;
}

function updateCategorySelect() {
    const select = document.getElementById('productCategory');
    if (!select) return;

    const currentValue = select.value;
    select.innerHTML = '<option value="">Choisir une catégorie</option>';

    categories.filter(c => c.active).forEach(category => {
        const option = document.createElement('option');
        option.value = category.slug;
        option.textContent = category.name;
        if (category.slug === currentValue) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// Charger la gestion des promotions
function loadPromotions() {
    const container = document.getElementById('promotionsContainer');
    if (!container) {
        console.error('Container promotionsContainer not found');
        return;
    }

    console.log('Loading promotions:', promotions);

    container.innerHTML = `
        <div class="promotions-header">
            <div class="promo-stats">
                <div class="stat-card">
                    <h3>Promotions actives</h3>
                    <div class="big-number">${promotions.filter(p => p.active && isPromoValid(p)).length}</div>
                </div>
                <div class="stat-card">
                    <h3>Promos Flash</h3>
                    <div class="big-number flash-count">${promotions.filter(p => p.flash && p.active).length}</div>
                </div>
                <div class="stat-card">
                    <h3>Utilisations totales</h3>
                    <div class="big-number">${promotions.reduce((sum, p) => sum + p.usageCount, 0)}</div>
                </div>
            </div>
            <div class="promo-quick-actions">
                <button onclick="createFlash24h()" class="btn-flash">
                    <i class="fas fa-bolt"></i> Promo Flash 24h
                </button>
                <button onclick="simulateUsage()" class="btn-test">
                    <i class="fas fa-chart-line"></i> Simuler utilisations
                </button>
                <button onclick="showAddPromoForm()" class="btn-save">
                    <i class="fas fa-plus"></i> Créer une promotion
                </button>
            </div>
        </div>

        <div id="promoFormContainer" class="promo-form-container" style="display: none;">
            <h3 id="promoFormTitle">Créer une nouvelle promotion</h3>
            <form id="promoForm" onsubmit="savePromoForm(event)">
                <input type="hidden" id="promoId" value="">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="promoName">Nom de la promotion *</label>
                        <input type="text" id="promoName" required placeholder="Ex: Promo Printemps">
                    </div>
                    <div class="form-group">
                        <label for="promoCode">Code promo *</label>
                        <input type="text" id="promoCode" required placeholder="Ex: SPRING20" style="text-transform: uppercase;">
                        <small>Lettres et chiffres uniquement, sans espaces</small>
                    </div>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="promoType">Type de réduction *</label>
                        <select id="promoType" onchange="updateDiscountLabel()">
                            <option value="percentage">Pourcentage (%)</option>
                            <option value="fixed">Montant fixe (DH)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="promoDiscount" id="discountLabel">Réduction (%) *</label>
                        <input type="number" id="promoDiscount" required min="1" max="100" placeholder="20">
                    </div>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="promoStartDate">Date de début *</label>
                        <input type="date" id="promoStartDate" required>
                    </div>
                    <div class="form-group">
                        <label for="promoEndDate">Date de fin *</label>
                        <input type="date" id="promoEndDate" required>
                    </div>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="promoMaxUsage">Nombre d'utilisations max</label>
                        <input type="number" id="promoMaxUsage" min="1" placeholder="100">
                        <small>Laisser vide pour illimité</small>
                    </div>
                    <div class="form-group">
                        <label for="promoMinAmount">Montant minimum de commande (DH)</label>
                        <input type="number" id="promoMinAmount" min="0" placeholder="0">
                        <small>Laisser vide ou 0 pour aucun minimum</small>
                    </div>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="promoActive" checked> Promotion active
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="promoFlash"> Promo Flash (affichage prioritaire)
                    </label>
                </div>
                <div style="text-align: center; margin-top: 1rem;">
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <button type="button" onclick="cancelPromoForm()" class="btn-cancel">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                </div>
            </form>
        </div>

        <div class="promotions-grid">
            ${promotions.map(promo => `
                <div class="promo-card ${promo.active ? 'active' : 'inactive'} ${promo.flash ? 'flash' : ''}">
                    <div class="promo-header">
                        <div class="promo-title">
                            <h3>${promo.name}</h3>
                            ${promo.flash ? '<span class="flash-badge">⚡ FLASH</span>' : ''}
                        </div>
                        <div class="promo-actions">
                            <button onclick="togglePromoStatus(${promo.id})" class="btn-toggle ${promo.active ? 'active' : 'inactive'}" title="${promo.active ? 'Désactiver' : 'Activer'}">
                                <i class="fas fa-power-off"></i>
                            </button>
                            <button onclick="togglePromoFlash(${promo.id})" class="btn-flash-toggle ${promo.flash ? 'flash-on' : 'flash-off'}" title="${promo.flash ? 'Désactiver Flash' : 'Activer Flash'}">
                                <i class="fas fa-bolt"></i>
                            </button>
                            <button onclick="editPromo(${promo.id})" class="btn-edit-small" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deletePromo(${promo.id})" class="btn-delete-small" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="promo-code">
                        <span class="code-label">Code:</span>
                        <span class="code-value">${promo.code}</span>
                        <button onclick="copyPromoCode('${promo.code}')" class="btn-copy">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="promo-discount">
                        <span class="discount-value">
                            ${promo.type === 'percentage' ? `-${promo.discount}%` : `-${promo.discount} DH`}
                        </span>
                    </div>
                    <div class="promo-dates">
                        <div class="date-range">
                            <i class="fas fa-calendar"></i>
                            ${formatDate(promo.startDate)} → ${formatDate(promo.endDate)}
                        </div>
                        <div class="promo-status ${isPromoValid(promo) ? 'valid' : 'expired'}">
                            ${getPromoStatusText(promo)}
                        </div>
                    </div>
                    <div class="promo-usage">
                        <div class="usage-bar">
                            <div class="usage-progress" style="width: ${getUsagePercentage(promo)}%"></div>
                        </div>
                        <div class="usage-text">
                            ${promo.usageCount} / ${promo.maxUsage || '∞'} utilisations
                        </div>
                    </div>
                    ${promo.minAmount ? `<div class="promo-min">Minimum: ${promo.minAmount} DH</div>` : ''}
                </div>
            `).join('')}
        </div>
    `;
}

// Fonctions pour la gestion des promotions
function showAddPromoForm() {
    document.getElementById('promoFormContainer').style.display = 'block';
    document.getElementById('promoFormTitle').textContent = 'Créer une nouvelle promotion';
    document.getElementById('promoForm').reset();
    document.getElementById('promoId').value = '';
    document.getElementById('promoActive').checked = true;
    document.getElementById('promoFlash').checked = false;

    // Définir les dates par défaut
    const today = new Date().toISOString().split('T')[0];
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const endDate = nextMonth.toISOString().split('T')[0];

    document.getElementById('promoStartDate').value = today;
    document.getElementById('promoEndDate').value = endDate;

    updateDiscountLabel();
}

function cancelPromoForm() {
    document.getElementById('promoFormContainer').style.display = 'none';
    document.getElementById('promoForm').reset();
}

function updateDiscountLabel() {
    const type = document.getElementById('promoType').value;
    const label = document.getElementById('discountLabel');
    const input = document.getElementById('promoDiscount');

    if (type === 'percentage') {
        label.textContent = 'Réduction (%) *';
        input.max = '100';
        input.placeholder = '20';
    } else {
        label.textContent = 'Réduction (DH) *';
        input.max = '10000';
        input.placeholder = '50';
    }
}

function savePromoForm(event) {
    event.preventDefault();

    const id = document.getElementById('promoId').value;
    const name = document.getElementById('promoName').value.trim();
    const code = document.getElementById('promoCode').value.trim().toUpperCase();
    const type = document.getElementById('promoType').value;
    const discount = parseFloat(document.getElementById('promoDiscount').value);
    const startDate = document.getElementById('promoStartDate').value;
    const endDate = document.getElementById('promoEndDate').value;
    const maxUsage = document.getElementById('promoMaxUsage').value ? parseInt(document.getElementById('promoMaxUsage').value) : null;
    const minAmount = document.getElementById('promoMinAmount').value ? parseFloat(document.getElementById('promoMinAmount').value) : 0;
    const active = document.getElementById('promoActive').checked;
    const flash = document.getElementById('promoFlash').checked;

    // Validations
    if (!name || !code || !discount || !startDate || !endDate) {
        alert('Veuillez remplir tous les champs obligatoires');
        return;
    }

    if (new Date(startDate) >= new Date(endDate)) {
        alert('La date de fin doit être après la date de début');
        return;
    }

    // Vérifier que le code est unique
    const existingPromo = promotions.find(p => p.code === code && p.id != id);
    if (existingPromo) {
        alert('Ce code promo existe déjà');
        return;
    }

    if (id) {
        // Modification
        const promoIndex = promotions.findIndex(p => p.id == id);
        if (promoIndex !== -1) {
            promotions[promoIndex] = {
                ...promotions[promoIndex],
                name,
                code,
                type,
                discount,
                startDate,
                endDate,
                maxUsage,
                minAmount,
                active,
                flash
            };
        }
    } else {
        // Ajout
        const newId = Math.max(...promotions.map(p => p.id), 0) + 1;
        promotions.push({
            id: newId,
            name,
            code,
            type,
            discount,
            startDate,
            endDate,
            maxUsage,
            minAmount,
            active,
            flash,
            usageCount: 0
        });
    }

    saveAllData();
    loadPromotions();
    cancelPromoForm();
}

function editPromo(id) {
    const promo = promotions.find(p => p.id === id);
    if (!promo) return;

    document.getElementById('promoFormContainer').style.display = 'block';
    document.getElementById('promoFormTitle').textContent = 'Modifier la promotion';
    document.getElementById('promoId').value = promo.id;
    document.getElementById('promoName').value = promo.name;
    document.getElementById('promoCode').value = promo.code;
    document.getElementById('promoType').value = promo.type;
    document.getElementById('promoDiscount').value = promo.discount;
    document.getElementById('promoStartDate').value = promo.startDate;
    document.getElementById('promoEndDate').value = promo.endDate;
    document.getElementById('promoMaxUsage').value = promo.maxUsage || '';
    document.getElementById('promoMinAmount').value = promo.minAmount || '';
    document.getElementById('promoActive').checked = promo.active;
    document.getElementById('promoFlash').checked = promo.flash || false;

    updateDiscountLabel();
}

function deletePromo(id) {
    const promo = promotions.find(p => p.id === id);
    if (!promo) return;

    if (!confirm(`Êtes-vous sûr de vouloir supprimer la promotion "${promo.name}" ?`)) {
        return;
    }

    promotions = promotions.filter(p => p.id !== id);
    saveAllData();
    loadPromotions();
}

function copyPromoCode(code) {
    navigator.clipboard.writeText(code).then(() => {
        // Afficher un feedback visuel
        const button = event.target.closest('.btn-copy');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.style.background = '#28a745';

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.style.background = '';
        }, 1000);
    });
}

function isPromoValid(promo) {
    const now = new Date();
    const start = new Date(promo.startDate);
    const end = new Date(promo.endDate);

    return promo.active && now >= start && now <= end &&
           (!promo.maxUsage || promo.usageCount < promo.maxUsage);
}

function getPromoStatusText(promo) {
    const now = new Date();
    const start = new Date(promo.startDate);
    const end = new Date(promo.endDate);

    if (!promo.active) return 'Inactive';
    if (now < start) return 'À venir';
    if (now > end) return 'Expirée';
    if (promo.maxUsage && promo.usageCount >= promo.maxUsage) return 'Épuisée';
    return 'Active';
}

function getUsagePercentage(promo) {
    if (!promo.maxUsage) return 0;
    return Math.min((promo.usageCount / promo.maxUsage) * 100, 100);
}

// ➕ Créer une promo flash 24h rapidement
function createFlash24h() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const startDate = today.toISOString().split('T')[0];
    const endDate = tomorrow.toISOString().split('T')[0];

    // Générer un code unique
    const codes = ['FLASH24', 'SUPER24', 'MEGA24', 'ULTRA24', 'POWER24'];
    let code = codes[Math.floor(Math.random() * codes.length)];

    // S'assurer que le code est unique
    let counter = 1;
    while (promotions.find(p => p.code === code)) {
        code = codes[Math.floor(Math.random() * codes.length)] + counter;
        counter++;
    }

    const newId = Math.max(...promotions.map(p => p.id), 0) + 1;
    const newPromo = {
        id: newId,
        name: "Flash Sale 24H",
        code: code,
        type: "percentage",
        discount: 25,
        startDate: startDate,
        endDate: endDate,
        maxUsage: 50,
        minAmount: 100,
        active: true,
        flash: true,
        usageCount: 0
    };

    promotions.push(newPromo);
    saveAllData();
    loadPromotions();

    // Afficher un message de succès avec le code
    showPromoSuccess(`Promo Flash 24h créée ! Code: ${code}`, code);
}

// 📊 Simuler des utilisations pour tester les barres de progression
function simulateUsage() {
    const activePromos = promotions.filter(p => p.active && p.maxUsage);

    if (activePromos.length === 0) {
        alert('Aucune promotion avec limite d\'utilisation trouvée !');
        return;
    }

    // Ajouter des utilisations aléatoirement
    activePromos.forEach(promo => {
        if (promo.usageCount < promo.maxUsage) {
            const additionalUsage = Math.floor(Math.random() * 5) + 1;
            promo.usageCount = Math.min(promo.usageCount + additionalUsage, promo.maxUsage);
        }
    });

    saveAllData();
    loadPromotions();

    showPromoSuccess('Utilisations simulées ! Observez les barres de progression 📊');
}

// ⚡ Activer/désactiver rapidement une promo flash
function togglePromoFlash(id) {
    const promo = promotions.find(p => p.id === id);
    if (!promo) return;

    promo.flash = !promo.flash;
    saveAllData();
    loadPromotions();

    const status = promo.flash ? 'activé' : 'désactivé';
    showPromoSuccess(`Mode Flash ${status} pour "${promo.name}" ⚡`);
}

// Activer/désactiver rapidement une promotion
function togglePromoStatus(id) {
    const promo = promotions.find(p => p.id === id);
    if (!promo) return;

    promo.active = !promo.active;
    saveAllData();
    loadPromotions();

    const status = promo.active ? 'activée' : 'désactivée';
    showPromoSuccess(`Promotion "${promo.name}" ${status} !`);
}

// Afficher un message de succès avec animation
function showPromoSuccess(message, code = null) {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = 'promo-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
            ${code ? `<button onclick="copyPromoCode('${code}')" class="btn-copy-notification">Copier ${code}</button>` : ''}
        </div>
    `;

    // Ajouter au DOM
    document.body.appendChild(notification);

    // Animation d'apparition
    setTimeout(() => notification.classList.add('show'), 100);

    // Suppression automatique
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 4000);
}

// Fonctions pour l'administration

// 🔐 Fonction de déconnexion
function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        localStorage.removeItem('arwa_admin_logged');
        window.location.href = 'login.html';
    }
}

// Afficher une notification admin
function showAdminNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `admin-notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="close-btn">×</button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => notification.classList.add('show'), 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// 🎫 GESTION DES PROMOTIONS

// Charger et afficher les promotions
function loadPromotions() {
    console.log('Chargement des promotions...');

    // Mettre à jour les statistiques
    updatePromotionStats();

    // Afficher les promotions
    displayPromotions();
}

// Mettre à jour les statistiques des promotions
function updatePromotionStats() {
    const activePromos = promotions.filter(p => p.active);
    const flashPromos = promotions.filter(p => p.flash && p.active);
    const totalUsage = promotions.reduce((sum, p) => sum + (p.usageCount || 0), 0);

    document.getElementById('activePromosCount').textContent = activePromos.length;
    document.getElementById('flashPromosCount').textContent = flashPromos.length;
    document.getElementById('totalUsageCount').textContent = totalUsage;
}

// Afficher les promotions dans la grille
function displayPromotions() {
    const container = document.getElementById('promotionsContainer');

    if (promotions.length === 0) {
        container.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 2rem;">
                <p>Aucune promotion créée. Cliquez sur "Créer une promotion" pour commencer.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = promotions.map(promo => `
        <div class="promo-card ${promo.flash ? 'flash' : ''} ${!promo.active ? 'inactive' : ''}">
            <div class="promo-header">
                <div class="promo-title">
                    <h3>${promo.name}</h3>
                    ${promo.flash ? '<span class="flash-badge">⚡ FLASH</span>' : ''}
                </div>
                <div class="promo-actions">
                    <button class="btn-toggle ${promo.active ? 'active' : 'inactive'}"
                            onclick="togglePromoStatus(${promo.id})" title="Activer/Désactiver">
                        ${promo.active ? '✓' : '✗'}
                    </button>
                    <button class="btn-flash-toggle ${promo.flash ? 'flash-on' : 'flash-off'}"
                            onclick="toggleFlashStatus(${promo.id})" title="Flash On/Off">
                        ⚡
                    </button>
                </div>
            </div>

            <div class="promo-code">
                <span class="code-label">Code:</span>
                <span class="code-value">${promo.code}</span>
            </div>

            <div class="promo-discount">
                <div class="discount-value">
                    ${promo.type === 'percentage' ? `-${promo.discount}%` : `-${promo.discount} DH`}
                </div>
            </div>

            <div class="promo-usage">
                <div class="usage-bar">
                    <div class="usage-progress" style="width: ${getUsagePercentage(promo)}%"></div>
                </div>
                <div class="usage-text">
                    ${promo.usageCount || 0}${promo.maxUsage ? ` / ${promo.maxUsage}` : ''} utilisations
                </div>
            </div>

            ${promo.minAmount ? `<div class="promo-min">Minimum: ${promo.minAmount} DH</div>` : ''}
        </div>
    `).join('');
}

// Calculer le pourcentage d'utilisation
function getUsagePercentage(promo) {
    if (!promo.maxUsage) return 30; // Valeur par défaut pour les promos illimitées
    return Math.min((promo.usageCount || 0) / promo.maxUsage * 100, 100);
}



// Activer/désactiver une promotion
function togglePromoStatus(id) {
    const promo = promotions.find(p => p.id === id);
    if (promo) {
        promo.active = !promo.active;
        saveAllData();
        loadPromotions();
        showPromoSuccess(`Promotion ${promo.active ? 'activée' : 'désactivée'} !`);
    }
}

// Activer/désactiver le statut Flash
function toggleFlashStatus(id) {
    const promo = promotions.find(p => p.id === id);
    if (promo) {
        promo.flash = !promo.flash;
        saveAllData();
        loadPromotions();
        showPromoSuccess(`Flash ${promo.flash ? 'activé' : 'désactivé'} !`);
    }
}

// Créer une promo Flash 24h
function createFlash24h() {
    const code = 'FLASH' + Math.random().toString(36).substr(2, 4).toUpperCase();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const newPromo = {
        id: Date.now(),
        name: 'Flash Sale 24H',
        code: code,
        discount: 25,
        type: 'percentage',
        active: true,
        flash: true,
        startDate: new Date().toISOString().split('T')[0],
        endDate: tomorrow.toISOString().split('T')[0],
        minAmount: 100,
        usageCount: 0,
        maxUsage: 50
    };

    promotions.unshift(newPromo);
    saveAllData();
    loadPromotions();

    showPromoSuccess(`Promo Flash 24h créée ! Code: ${code}`);
}

// Simuler des utilisations
function simulateUsage() {
    promotions.forEach(promo => {
        if (promo.active) {
            const randomUsage = Math.floor(Math.random() * 5) + 1;
            promo.usageCount = (promo.usageCount || 0) + randomUsage;
        }
    });

    saveAllData();
    loadPromotions();
    showPromoSuccess('Utilisations simulées avec succès !');
}

// Afficher le formulaire de création de promotion
function showCreatePromoForm() {
    const formContainer = document.getElementById('promoFormContainer');
    const formTitle = document.getElementById('formTitle');

    // Réinitialiser le formulaire
    document.getElementById('promoForm').reset();
    formTitle.textContent = 'Créer une nouvelle promotion';

    // Définir les dates par défaut
    const today = new Date().toISOString().split('T')[0];
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const endDate = nextMonth.toISOString().split('T')[0];

    document.getElementById('startDate').value = today;
    document.getElementById('endDate').value = endDate;

    // Afficher le formulaire
    formContainer.style.display = 'block';
    formContainer.scrollIntoView({ behavior: 'smooth' });
}

// Masquer le formulaire de création
function hideCreatePromoForm() {
    const formContainer = document.getElementById('promoFormContainer');
    formContainer.style.display = 'none';
}

// Mettre à jour l'input de réduction selon le type
function updateDiscountInput() {
    const discountType = document.getElementById('discountType').value;
    const discountValue = document.getElementById('discountValue');

    if (discountType === 'percentage') {
        discountValue.max = '100';
        discountValue.placeholder = 'Ex: 20 pour 20%';
    } else {
        discountValue.max = '1000';
        discountValue.placeholder = 'Ex: 50 pour 50 DH';
    }
}

// Sauvegarder une promotion (création ou modification)
function savePromotion(event) {
    event.preventDefault();

    const promoData = {
        id: Date.now(),
        name: document.getElementById('promoName').value,
        code: document.getElementById('promoCode').value.toUpperCase(),
        discount: parseInt(document.getElementById('discountValue').value),
        type: document.getElementById('discountType').value,
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        minAmount: parseInt(document.getElementById('minAmount').value) || 0,
        maxUsage: parseInt(document.getElementById('maxUsage').value) || null,
        flash: document.getElementById('isFlash').checked,
        active: true,
        usageCount: 0
    };

    // Validation
    if (!validatePromotion(promoData)) {
        return;
    }

    // Vérifier si le code existe déjà
    if (promotions.some(p => p.code === promoData.code)) {
        showPromoSuccess('❌ Ce code promo existe déjà !');
        return;
    }

    // Ajouter la promotion
    promotions.unshift(promoData);
    saveAllData();
    loadPromotions();
    hideCreatePromoForm();

    showPromoSuccess(`✅ Promotion "${promoData.name}" créée avec succès !`);
}

// Valider les données de la promotion
function validatePromotion(promo) {
    // Vérifier les dates
    const startDate = new Date(promo.startDate);
    const endDate = new Date(promo.endDate);

    if (endDate <= startDate) {
        showPromoSuccess('❌ La date de fin doit être après la date de début !');
        return false;
    }

    // Vérifier la réduction
    if (promo.type === 'percentage' && (promo.discount < 1 || promo.discount > 100)) {
        showPromoSuccess('❌ Le pourcentage doit être entre 1 et 100 !');
        return false;
    }

    if (promo.type === 'fixed' && promo.discount < 1) {
        showPromoSuccess('❌ Le montant de réduction doit être supérieur à 0 !');
        return false;
    }

    // Vérifier le code promo
    if (promo.code.length < 3) {
        showPromoSuccess('❌ Le code promo doit contenir au moins 3 caractères !');
        return false;
    }

    return true;
}

// Afficher une notification de succès pour les promotions
function showPromoSuccess(message) {
    const notification = document.createElement('div');
    notification.className = 'promo-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => notification.classList.add('show'), 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Initialiser au chargement
document.addEventListener('DOMContentLoaded', function() {
    loadAllData();

    // Forcer la sauvegarde des données par défaut si elles n'existent pas
    if (!localStorage.getItem('arwa_promotions')) {
        console.log('Initializing default promotions data');
        saveAllData();

        // Forcer la sauvegarde immédiate des promotions
        localStorage.setItem('arwa_promotions', JSON.stringify(promotions));
        console.log('Promotions saved to localStorage:', promotions);
    }

    // Charger le dashboard par défaut
    setTimeout(() => {
        loadDashboard();
    }, 500);
});
