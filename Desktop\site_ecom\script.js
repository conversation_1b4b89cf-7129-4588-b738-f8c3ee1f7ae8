// Base de données des produits
const products = [
    {
        id: 1,
        name: "Robe d'été florale",
        price: 499,
        category: "robes",
        image: "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        description: "Une magnifique robe d'été avec un motif floral délicat. Parfaite pour les journées ensoleillées.",
        sizes: ["XS", "S", "M", "L", "XL"],
        colors: ["Rose", "Blanc", "Bleu"]
    },
    {
        id: 2,
        name: "Blouse élégante",
        price: 399,
        category: "tops",
        image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        description: "Blouse élégante en soie, idéale pour le bureau ou les occasions spéciales.",
        sizes: ["XS", "S", "M", "L", "XL"],
        colors: ["<PERSON>", "Noir", "Beige"]
    },
    {
        id: 3,
        name: "<PERSON>pe midi chic",
        price: 349,
        category: "jupes",
        image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        description: "Jupe midi élégante qui s'adapte à toutes les morphologies.",
        sizes: ["XS", "S", "M", "L", "XL"],
        colors: ["Noir", "Marine", "Camel"]
    },
    {
        id: 4,
        name: "Pantalon tailleur",
        price: 599,
        category: "pantalons",
        image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        description: "Pantalon tailleur confortable et élégant pour un look professionnel.",
        sizes: ["XS", "S", "M", "L", "XL"],
        colors: ["Noir", "Gris", "Marine"]
    },
    {
        id: 5,
        name: "Robe de soirée",
        price: 899,
        category: "robes",
        image: "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        description: "Robe de soirée sophistiquée pour vos événements spéciaux.",
        sizes: ["XS", "S", "M", "L", "XL"],
        colors: ["Noir", "Rouge", "Bleu nuit"]
    },
    {
        id: 6,
        name: "Top casual",
        price: 249,
        category: "tops",
        image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        description: "Top casual confortable pour un look décontracté.",
        sizes: ["XS", "S", "M", "L", "XL"],
        colors: ["Rose", "Blanc", "Gris"]
    }
];

// Gestion du panier
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();
    
    // Navigation mobile
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }
    
    // Initialiser les pages spécifiques
    const currentPage = window.location.pathname.split('/').pop();
    
    switch(currentPage) {
        case 'boutique.html':
            initBoutique();
            break;
        case 'produit.html':
            initProductDetail();
            break;
        case 'panier.html':
            initCart();
            break;
        case 'contact.html':
            initContact();
            break;
    }
});

// Fonctions du panier
function addToCart(productId, size = 'M', color = null, quantity = 1) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const cartItem = {
        id: productId,
        name: product.name,
        price: product.price,
        image: product.image,
        size: size,
        color: color || product.colors[0],
        quantity: quantity
    };
    
    // Vérifier si l'article existe déjà
    const existingItem = cart.find(item => 
        item.id === productId && item.size === size && item.color === color
    );
    
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push(cartItem);
    }
    
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    
    // Animation d'ajout au panier
    showNotification('Produit ajouté au panier !');
}

function removeFromCart(productId, size, color) {
    cart = cart.filter(item => 
        !(item.id === productId && item.size === size && item.color === color)
    );
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    
    if (window.location.pathname.includes('panier.html')) {
        initCart();
    }
}

function updateQuantity(productId, size, color, newQuantity) {
    const item = cart.find(item => 
        item.id === productId && item.size === size && item.color === color
    );
    
    if (item) {
        if (newQuantity <= 0) {
            removeFromCart(productId, size, color);
        } else {
            item.quantity = newQuantity;
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            
            if (window.location.pathname.includes('panier.html')) {
                initCart();
            }
        }
    }
}

function updateCartCount() {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
    }
}

function getCartTotal() {
    return cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
}

// Page Boutique
function initBoutique() {
    displayProducts(products);
}

function displayProducts(productsToShow) {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;
    
    productsGrid.innerHTML = '';
    
    productsToShow.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.setAttribute('data-product-id', product.id);
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.name}">
            <div class="product-overlay">
                <button class="btn-quick-view" onclick="viewProduct(${product.id})">Voir détails</button>
            </div>
        </div>
        <div class="product-info">
            <h3 class="product-name">${product.name}</h3>
            <p class="product-price">${product.price} DH</p>
            <button class="btn btn-add-cart" onclick="addToCart(${product.id})">ACHETER</button>
        </div>
    `;
    
    return card;
}

function filterProducts() {
    const category = document.getElementById('category').value;
    const priceRange = document.getElementById('price').value;
    
    let filteredProducts = products;
    
    // Filtrer par catégorie
    if (category !== 'all') {
        filteredProducts = filteredProducts.filter(product => product.category === category);
    }
    
    // Filtrer par prix
    if (priceRange !== 'all') {
        const [min, max] = priceRange.split('-').map(Number);
        filteredProducts = filteredProducts.filter(product => {
            return product.price >= min && (max ? product.price <= max : true);
        });
    }
    
    displayProducts(filteredProducts);
}

function sortProducts() {
    const sortBy = document.getElementById('sort').value;
    let sortedProducts = [...products];
    
    switch(sortBy) {
        case 'name':
            sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'price-low':
            sortedProducts.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            sortedProducts.sort((a, b) => b.price - a.price);
            break;
    }
    
    displayProducts(sortedProducts);
}

// Page Détail Produit
function viewProduct(productId) {
    localStorage.setItem('currentProduct', productId);
    window.location.href = 'produit.html';
}

function initProductDetail() {
    const productId = parseInt(localStorage.getItem('currentProduct'));
    const product = products.find(p => p.id === productId);
    
    if (!product) {
        window.location.href = 'boutique.html';
        return;
    }
    
    displayProductDetail(product);
    displayRelatedProducts(product);
}

function displayProductDetail(product) {
    const breadcrumb = document.getElementById('productBreadcrumb');
    const content = document.getElementById('productDetailContent');
    
    if (breadcrumb) {
        breadcrumb.textContent = product.name;
    }
    
    if (content) {
        content.innerHTML = `
            <div class="product-detail-grid">
                <div class="product-images">
                    <div class="main-image">
                        <img src="${product.image}" alt="${product.name}">
                    </div>
                </div>
                <div class="product-details">
                    <h1>${product.name}</h1>
                    <p class="product-price">${product.price} DH</p>
                    <p class="product-description">${product.description}</p>
                    
                    <div class="product-options">
                        <div class="option-group">
                            <label>Taille:</label>
                            <div class="size-options">
                                ${product.sizes.map(size => 
                                    `<div class="size-option" onclick="selectSize('${size}')">${size}</div>`
                                ).join('')}
                            </div>
                        </div>
                        
                        <div class="option-group">
                            <label>Couleur:</label>
                            <div class="color-options">
                                ${product.colors.map(color => 
                                    `<div class="color-option" onclick="selectColor('${color}')">${color}</div>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                    
                    <div class="add-to-cart-section">
                        <div class="quantity-selector">
                            <label>Quantité:</label>
                            <input type="number" id="quantity" value="1" min="1" max="10">
                        </div>
                        <button class="btn btn-primary" onclick="addProductToCart(${product.id})">
                            ACHETER
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Sélectionner la première taille et couleur par défaut
        setTimeout(() => {
            selectSize(product.sizes[0]);
            selectColor(product.colors[0]);
        }, 100);
    }
}

let selectedSize = null;
let selectedColor = null;

function selectSize(size) {
    selectedSize = size;
    document.querySelectorAll('.size-option').forEach(option => {
        option.classList.remove('selected');
    });
    event.target.classList.add('selected');
}

function selectColor(color) {
    selectedColor = color;
    document.querySelectorAll('.color-option').forEach(option => {
        option.classList.remove('selected');
    });
    event.target.classList.add('selected');
}

function addProductToCart(productId) {
    const quantity = parseInt(document.getElementById('quantity').value);
    
    if (!selectedSize || !selectedColor) {
        showNotification('Veuillez sélectionner une taille et une couleur');
        return;
    }
    
    addToCart(productId, selectedSize, selectedColor, quantity);
}

function displayRelatedProducts(currentProduct) {
    const relatedContainer = document.getElementById('relatedProducts');
    if (!relatedContainer) return;

    const relatedProducts = products
        .filter(p => p.id !== currentProduct.id && p.category === currentProduct.category)
        .slice(0, 3);

    relatedContainer.innerHTML = '';
    relatedProducts.forEach(product => {
        const card = createProductCard(product);
        relatedContainer.appendChild(card);
    });
}

// Page Panier
function initCart() {
    displayCartItems();
    updateCartSummary();
}

function displayCartItems() {
    const cartItems = document.getElementById('cartItems');
    const emptyCart = document.getElementById('emptyCart');

    if (cart.length === 0) {
        if (cartItems) cartItems.style.display = 'none';
        if (emptyCart) emptyCart.style.display = 'block';
        return;
    }

    if (cartItems) cartItems.style.display = 'block';
    if (emptyCart) emptyCart.style.display = 'none';

    if (cartItems) {
        cartItems.innerHTML = cart.map(item => `
            <div class="cart-item">
                <div class="cart-item-image">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="cart-item-info">
                    <h3 class="cart-item-name">${item.name}</h3>
                    <p>Taille: ${item.size} | Couleur: ${item.color}</p>
                    <p class="cart-item-price">${item.price} DH</p>
                </div>
                <div class="cart-item-controls">
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, '${item.size}', '${item.color}', ${item.quantity - 1})">-</button>
                        <input type="number" class="quantity-input" value="${item.quantity}"
                               onchange="updateQuantity(${item.id}, '${item.size}', '${item.color}', parseInt(this.value))" min="1">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, '${item.size}', '${item.color}', ${item.quantity + 1})">+</button>
                    </div>
                    <button class="remove-btn" onclick="removeFromCart(${item.id}, '${item.size}', '${item.color}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }
}

function updateCartSummary() {
    const subtotal = getCartTotal();
    const shipping = subtotal >= 500 ? 0 : 59;
    const total = subtotal + shipping;

    const subtotalElement = document.getElementById('subtotal');
    const shippingElement = document.getElementById('shipping');
    const totalElement = document.getElementById('total');

    if (subtotalElement) subtotalElement.textContent = `${subtotal} DH`;
    if (shippingElement) shippingElement.textContent = shipping === 0 ? 'Gratuite' : `${shipping} DH`;
    if (totalElement) totalElement.textContent = `${total} DH`;
}

function applyPromo() {
    const promoInput = document.getElementById('promoInput');
    const promoCode = promoInput.value.toUpperCase();

    const validCodes = {
        'SPRING20': 0.2,
        'WELCOME10': 0.1,
        'SUMMER15': 0.15
    };

    if (validCodes[promoCode]) {
        const discount = validCodes[promoCode];
        showNotification(`Code promo appliqué ! -${(discount * 100)}%`);
        // Ici vous pourriez appliquer la réduction
    } else {
        showNotification('Code promo invalide', 'error');
    }

    promoInput.value = '';
}

function checkout() {
    if (cart.length === 0) {
        showNotification('Votre panier est vide', 'error');
        return;
    }

    // Simulation de commande
    showNotification('Commande validée ! Merci pour votre achat.');
    cart = [];
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    initCart();
}

// Page Contact
function initContact() {
    // Initialisation de la page contact si nécessaire
}

function submitContactForm(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData);

    // Validation simple
    if (!data.name || !data.email || !data.subject || !data.message) {
        showNotification('Veuillez remplir tous les champs obligatoires', 'error');
        return;
    }

    // Simulation d'envoi
    showNotification('Message envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.');
    event.target.reset();
}

// Fonctions utilitaires
function showNotification(message, type = 'success') {
    // Créer la notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // Ajouter les styles si pas encore fait
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #d4edda;
                color: #155724;
                padding: 1rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                animation: slideIn 0.3s ease;
            }
            .notification.error {
                background: #f8d7da;
                color: #721c24;
            }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            .notification button {
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: inherit;
            }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }

    document.body.appendChild(notification);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Animation au scroll
function animateOnScroll() {
    const elements = document.querySelectorAll('.product-card, .about-text, .contact-item');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
            }
        });
    });

    elements.forEach(element => {
        observer.observe(element);
    });
}

// Initialiser les animations au chargement
document.addEventListener('DOMContentLoaded', animateOnScroll);
