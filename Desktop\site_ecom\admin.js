// Variables globales pour l'administration
let editingProductId = null;
let productColors = [];

// Vérification de la sécurité
function checkAdminAccess() {
    const isLogged = sessionStorage.getItem('arwa_admin_logged');
    const loginTime = sessionStorage.getItem('arwa_admin_time');
    const currentTime = Date.now();
    const sessionDuration = 2 * 60 * 60 * 1000; // 2 heures

    if (isLogged !== 'true' || !loginTime || (currentTime - loginTime > sessionDuration)) {
        // Rediriger vers la page de connexion
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// Fonction de déconnexion
function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        sessionStorage.removeItem('arwa_admin_logged');
        sessionStorage.removeItem('arwa_admin_time');
        window.location.href = 'login.html';
    }
}

// Initialisation de l'interface admin
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier l'accès avant de charger l'interface
    if (!checkAdminAccess()) {
        return;
    }

    // Charger le tableau de bord par défaut
    showSection('dashboard');
    updateStats();
});

// Afficher/masquer les sections
// La fonction showSection est maintenant dans admin-extended.js

// Charger la table des produits avec ordre stable
function loadProductsTable() {
    const tbody = document.getElementById('productsTableBody');

    // Trier les produits par ID pour un ordre stable
    const sortedProducts = [...products].sort((a, b) => a.id - b.id);

    // Vider le tableau avec une transition douce
    tbody.style.opacity = '0.5';

    setTimeout(() => {
        tbody.innerHTML = '';

        sortedProducts.forEach((product, index) => {
            const row = document.createElement('tr');
            row.style.animationDelay = `${index * 0.05}s`;
            row.className = 'table-row-stable';
            row.setAttribute('data-product-id', product.id);
            row.innerHTML = `
                <td>${product.id}</td>
                <td><img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/60x60?text=Image'"></td>
                <td>${product.name}</td>
                <td>${product.price} DH</td>
                <td>${getCategoryName(product.category)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-edit" onclick="editProduct(${product.id})">
                            <i class="fas fa-edit"></i> Modifier
                        </button>
                        <button class="btn-delete" onclick="deleteProduct(${product.id})">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        // Restaurer l'opacité
        tbody.style.opacity = '1';
    }, 100);
}

// Obtenir le nom de la catégorie
function getCategoryName(category) {
    const categories = {
        'robes': 'Robes',
        'tops': 'Tops & Blouses',
        'jupes': 'Jupes',
        'pantalons': 'Pantalons'
    };
    return categories[category] || category;
}

// Mettre à jour les statistiques avec animation stable
function updateStats() {
    const stats = {
        totalProducts: products.length,
        totalRobes: products.filter(p => p.category === 'robes').length,
        totalTops: products.filter(p => p.category === 'tops').length,
        totalJupes: products.filter(p => p.category === 'jupes').length,
        totalPantalons: products.filter(p => p.category === 'pantalons').length
    };

    // Mise à jour avec animation douce
    Object.keys(stats).forEach(statKey => {
        const element = document.getElementById(statKey);
        if (element) {
            const currentValue = parseInt(element.textContent) || 0;
            const newValue = stats[statKey];

            if (currentValue !== newValue) {
                element.style.transform = 'scale(1.1)';
                element.style.color = '#d63384';

                setTimeout(() => {
                    element.textContent = newValue;
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 150);
            }
        }
    });
}

// Ajouter une couleur
function addColor() {
    const colorInput = document.getElementById('newColor');
    const color = colorInput.value.trim();
    
    if (color && !productColors.includes(color)) {
        productColors.push(color);
        updateColorsDisplay();
        colorInput.value = '';
    }
}

// Supprimer une couleur
function removeColor(color) {
    productColors = productColors.filter(c => c !== color);
    updateColorsDisplay();
}

// Mettre à jour l'affichage des couleurs
function updateColorsDisplay() {
    const container = document.getElementById('colorsContainer');
    container.innerHTML = '';
    
    productColors.forEach(color => {
        const colorTag = document.createElement('div');
        colorTag.className = 'color-tag';
        colorTag.innerHTML = `
            ${color}
            <button type="button" onclick="removeColor('${color}')">×</button>
        `;
        container.appendChild(colorTag);
    });
}

// Réinitialiser le formulaire
function resetForm() {
    document.getElementById('productForm').reset();
    document.getElementById('productId').value = '';
    document.getElementById('formTitle').innerHTML = '<i class="fas fa-plus"></i> Ajouter un nouveau produit';
    editingProductId = null;
    productColors = [];
    updateColorsDisplay();
    
    // Cocher M par défaut
    document.querySelector('input[value="M"].size-checkbox').checked = true;
    
    hideMessages();
}

// Modifier un produit
function editProduct(id) {
    const product = products.find(p => p.id === id);
    if (!product) return;
    
    editingProductId = id;
    document.getElementById('formTitle').innerHTML = '<i class="fas fa-edit"></i> Modifier le produit';
    
    // Remplir le formulaire
    document.getElementById('productId').value = product.id;
    document.getElementById('productName').value = product.name;
    document.getElementById('productPrice').value = product.price;
    document.getElementById('productCategory').value = product.category;
    document.getElementById('productImage').value = product.image;
    document.getElementById('productDescription').value = product.description;
    
    // Cocher les tailles
    document.querySelectorAll('.size-checkbox').forEach(checkbox => {
        checkbox.checked = product.sizes.includes(checkbox.value);
    });
    
    // Charger les couleurs
    productColors = [...product.colors];
    updateColorsDisplay();
    
    // Aller à la section d'ajout
    showSection('add');
    hideMessages();
}

// Supprimer un produit avec stabilité
function deleteProduct(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
        const index = products.findIndex(p => p.id === id);
        if (index !== -1) {
            // Animation de suppression
            const row = document.querySelector(`tr[data-product-id="${id}"]`);
            if (row) {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-20px)';
            }

            setTimeout(() => {
                products.splice(index, 1);
                loadProductsTable();
                updateStats();
                showMessage('listSuccessMessage', 'Produit supprimé avec succès !');

                // Sauvegarder dans localStorage pour persistance
                localStorage.setItem('arwa_products', JSON.stringify(products));
            }, 300);
        }
    }
}

// Annuler l'édition
function cancelEdit() {
    resetForm();
    showSection('list');
}

// Gérer la soumission du formulaire
document.getElementById('productForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Récupérer les données du formulaire
    const formData = {
        name: document.getElementById('productName').value.trim(),
        price: parseInt(document.getElementById('productPrice').value),
        category: document.getElementById('productCategory').value,
        image: document.getElementById('productImage').value.trim(),
        description: document.getElementById('productDescription').value.trim(),
        sizes: Array.from(document.querySelectorAll('.size-checkbox:checked')).map(cb => cb.value),
        colors: [...productColors]
    };
    
    // Validation
    if (!formData.name || !formData.price || !formData.category || !formData.image || !formData.description) {
        showMessage('formErrorMessage', 'Veuillez remplir tous les champs obligatoires.');
        return;
    }
    
    if (formData.sizes.length === 0) {
        showMessage('formErrorMessage', 'Veuillez sélectionner au moins une taille.');
        return;
    }
    
    if (formData.colors.length === 0) {
        showMessage('formErrorMessage', 'Veuillez ajouter au moins une couleur.');
        return;
    }
    
    if (editingProductId) {
        // Modifier un produit existant
        const index = products.findIndex(p => p.id === editingProductId);
        if (index !== -1) {
            products[index] = { ...products[index], ...formData };
            showMessage('formSuccessMessage', 'Produit modifié avec succès !');
        }
    } else {
        // Ajouter un nouveau produit
        const newId = Math.max(...products.map(p => p.id)) + 1;
        const newProduct = { id: newId, ...formData };
        products.push(newProduct);
        showMessage('formSuccessMessage', 'Produit ajouté avec succès !');
    }
    
    // Sauvegarder dans localStorage pour persistance
    localStorage.setItem('arwa_products', JSON.stringify(products));
    
    // Mettre à jour l'affichage
    loadProductsTable();
    updateStats();
    
    // Réinitialiser le formulaire après un délai
    setTimeout(() => {
        resetForm();
        showSection('list');
    }, 2000);
});

// Afficher un message
function showMessage(elementId, message) {
    hideMessages();
    const element = document.getElementById(elementId);
    element.textContent = message;
    element.style.display = 'block';
    
    // Masquer automatiquement après 5 secondes
    setTimeout(() => {
        element.style.display = 'none';
    }, 5000);
}

// Masquer tous les messages
function hideMessages() {
    document.querySelectorAll('.success-message, .error-message').forEach(msg => {
        msg.style.display = 'none';
    });
}

// Charger les produits depuis localStorage au démarrage
function loadProductsFromStorage() {
    const savedProducts = localStorage.getItem('arwa_products');
    if (savedProducts) {
        try {
            const parsedProducts = JSON.parse(savedProducts);
            // Remplacer les produits actuels par ceux sauvegardés
            products.length = 0;
            products.push(...parsedProducts);
        } catch (e) {
            console.error('Erreur lors du chargement des produits:', e);
        }
    }
}

// Sauvegarder les produits dans localStorage
function saveProductsToStorage() {
    localStorage.setItem('arwa_products', JSON.stringify(products));
}

// Charger les produits au démarrage
loadProductsFromStorage();

// Fonction pour exporter les produits (bonus)
function exportProducts() {
    const dataStr = JSON.stringify(products, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'arwa_shop_products.json';
    link.click();
    URL.revokeObjectURL(url);
}

// Ajouter un bouton d'export (optionnel)
document.addEventListener('DOMContentLoaded', function() {
    const adminHeader = document.querySelector('.admin-header');
    const exportBtn = document.createElement('button');
    exportBtn.innerHTML = '<i class="fas fa-download"></i> Exporter les produits';
    exportBtn.style.cssText = 'background: rgba(255,255,255,0.2); color: white; border: 1px solid white; padding: 8px 16px; border-radius: 20px; margin-top: 1rem; cursor: pointer;';
    exportBtn.onclick = exportProducts;
    adminHeader.appendChild(exportBtn);
});
